using System.Collections;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;

/// <summary>
/// 场景R通道统一颜色控制器
/// 
/// 功能：
/// - 统一控制场景内所有SetVertexColor组件的R通道9个颜色区间
/// - 支持预览模式和应用模式
/// - 自动检测和应用到场景内的所有相关组件
/// </summary>
[ExecuteAlways]
public class SceneRChannelController : MonoBehaviour
{
    [Header("R通道统一颜色设置")]
    [SerializeField]
    public Color[] rChannelColors = new Color[9];

    // 缓存场景内所有的SetVertexColor组件
    private SetVertexColor[] sceneComponents;

    // 标记是否处于预览模式
    private bool isInPreviewMode = false;

    // 存储原始R通道颜色（用于预览和重置）
    private Dictionary<SetVertexColor, Color[]> originalRChannelColors = new Dictionary<SetVertexColor, Color[]>();

    // 存储场景中有效的R通道区间信息
    private HashSet<float> effectiveRValues = new HashSet<float>();
    private bool analysisValid = false;

    // R通道区间值定义（从小到大排列）
    private static readonly float[] R_SECTION_VALUES = {
        0.1f, 0.1889f, 0.2778f, 0.3667f, 0.4556f,
        0.5444f, 0.6333f, 0.7222f, 0.9f
    };

#if UNITY_EDITOR
    void OnValidate()
    {
        // 确保rChannelColors数组大小为9
        if (rChannelColors == null || rChannelColors.Length != 9)
        {
            Color[] newColors = new Color[9];
            if (rChannelColors != null)
            {
                for (int i = 0; i < Mathf.Min(rChannelColors.Length, 9); i++)
                {
                    newColors[i] = rChannelColors[i];
                }
            }
            rChannelColors = newColors;
        }

        // 实时应用颜色变化
        ApplyRChannelColors(false);
    }
#endif

    private void Reset()
    {
        // 初始化9个R通道颜色：从亮红色到暗红色
        for (int i = 0; i < 9; i++)
        {
            float brightness = 1.0f - (float)i / 8.0f;
            rChannelColors[i] = new Color(brightness, 0, 0, 1.0f);
        }
    }

    private void Awake()
    {
        // 确保颜色数组已初始化
        InitializeColorsIfNeeded();
        
        // 立即应用R通道颜色到全局Shader属性，避免场景加载时的闪烁
        for (int i = 0; i < 9 && i < rChannelColors.Length; i++)
        {
            string propertyName = $"_ColorR{i}";
            Shader.SetGlobalColor(propertyName, rChannelColors[i]);
        }
    }

    private void Start()
    {
        // 确保R通道颜色已初始化
        InitializeColorsIfNeeded();
        
        // 在Start中也进行一次分析，确保场景完全加载后的分析
        RefreshSceneComponents();
        
        // 确保立即应用R通道颜色
        ApplyRChannelColors(false);
        
        // 多层延迟初始化，确保挂载后立即生效
#if UNITY_EDITOR
        // 第一层：立即刷新
        UnityEditor.EditorApplication.delayCall += () => {
            ForceRefreshSetVertexColorEditors();
        };
        
        // 第二层：再次延迟刷新，确保其他组件也完成初始化
        UnityEditor.EditorApplication.delayCall += () => {
            UnityEditor.EditorApplication.delayCall += () => {
                RefreshSceneComponents();
                ForceRefreshSetVertexColorEditors();
            };
        };
        
        // 第三层：最终确保，处理异步组件初始化
        UnityEditor.EditorApplication.delayCall += () => {
            UnityEditor.EditorApplication.delayCall += () => {
                UnityEditor.EditorApplication.delayCall += () => {
                    RefreshSceneComponents();
                    ForceRefreshSetVertexColorEditors();
                    Debug.Log("R通道统一控制器：延迟初始化完成");
                };
            };
        };
#endif
    }

    private void OnEnable()
    {
        // 刷新场景组件、分析R通道使用情况并保存原始颜色
        RefreshSceneComponents();
        SaveOriginalRChannelColors();
        
        // 多层延迟刷新，确保重新启用后立即生效
#if UNITY_EDITOR
        // 第一层：立即刷新
        UnityEditor.EditorApplication.delayCall += () => {
            ForceRefreshSetVertexColorEditors();
        };
        
        // 第二层：再次延迟刷新，确保其他组件也完成初始化
        UnityEditor.EditorApplication.delayCall += () => {
            UnityEditor.EditorApplication.delayCall += () => {
                RefreshSceneComponents();
                ForceRefreshSetVertexColorEditors();
            };
        };
#endif
    }

    // 初始化颜色数组
    private void InitializeColorsIfNeeded()
    {
        if (rChannelColors == null)
        {
            rChannelColors = new Color[9];
        }
        else if (rChannelColors.Length != 9)
        {
            Color[] newColors = new Color[9];
            for (int i = 0; i < Mathf.Min(rChannelColors.Length, 9); i++)
            {
                newColors[i] = rChannelColors[i];
            }
            rChannelColors = newColors;
        }

        // 检查是否需要初始化颜色值
        bool needsInitialization = false;
        for (int i = 0; i < 9; i++)
        {
            if (rChannelColors[i].r == 0 && rChannelColors[i].g == 0 &&
                rChannelColors[i].b == 0 && rChannelColors[i].a == 0)
            {
                needsInitialization = true;
                break;
            }
        }

        if (needsInitialization)
        {
            for (int i = 0; i < 9; i++)
            {
                float brightness = 1.0f - (float)i / 8.0f;
                rChannelColors[i] = new Color(brightness, 0, 0, 1.0f);
            }
        }
    }

    // 刷新场景组件列表并分析所有R通道使用情况
    public void RefreshSceneComponents()
    {
        sceneComponents = FindObjectsOfType<SetVertexColor>();

        // 分析场景中模型的实际R通道使用情况
        // 这会检测哪些R通道区间在场景中真的有效果
        AnalyzeAllRChannelUsage();
        
        // 立即应用当前的R通道颜色设置到所有组件（包括新复制的）
        ApplyRChannelColors(false);
    }

    // 收集各个SetVertexColor组件的R通道分析数据
    private void AnalyzeAllRChannelUsage()
    {
        effectiveRValues.Clear();
        
        if (sceneComponents == null) return;

        // 收集所有组件的R通道有效区间数据
        foreach (var component in sceneComponents)
        {
            if (component == null) continue;

            // 获取每个组件的R通道有效区间数据（这些数据来自SetVertexColorEditor的分析）
            var componentRValues = component.GetEffectiveRValues();
            if (componentRValues != null)
            {
                foreach (var rValue in componentRValues)
                {
                    effectiveRValues.Add(rValue);
                }
            }
        }

        analysisValid = true;

        // 输出分析结果
        if (effectiveRValues.Count > 0)
        {
            var sortedValues = effectiveRValues.OrderByDescending(v => v);
            string valuesStr = string.Join(", ", sortedValues.Select(v => v.ToString("F4")));
            Debug.Log($"场景R通道分析完成，检测到 {effectiveRValues.Count} 个有效区间: {valuesStr}");
        }
        else
        {
            Debug.Log($"场景R通道分析完成，未检测到有效的R通道区间");
        }

        // 强制刷新所有SetVertexColorEditor的显示
        ForceRefreshSetVertexColorEditors();
    }

    // 强制刷新所有SetVertexColorEditor的显示
    private void ForceRefreshSetVertexColorEditors()
    {
#if UNITY_EDITOR
        // 方法1：强制刷新所有SetVertexColor组件
        if (sceneComponents != null)
        {
            foreach (var component in sceneComponents)
            {
                if (component != null)
                {
                    UnityEditor.EditorUtility.SetDirty(component);
                }
            }
        }

        // 方法2：强制重绘Inspector
        UnityEditor.EditorApplication.QueuePlayerLoopUpdate();
        
        // 方法3：延迟强制重绘，确保界面更新
        UnityEditor.EditorApplication.delayCall += () => {
            // 强制重绘所有Inspector窗口
            var inspectorType = typeof(UnityEditor.Editor).Assembly.GetType("UnityEditor.InspectorWindow");
            if (inspectorType != null)
            {
                UnityEditor.EditorWindow.GetWindow(inspectorType).Repaint();
            }
            
            // 如果有选中的对象，强制重新选择以触发Inspector刷新
            if (UnityEditor.Selection.activeGameObject != null)
            {
                var selected = UnityEditor.Selection.activeGameObject;
                UnityEditor.Selection.activeGameObject = null;
                UnityEditor.EditorApplication.delayCall += () => {
                    UnityEditor.Selection.activeGameObject = selected;
                };
            }
        };
#endif
    }

    // 保存原始R通道颜色（现在保存SceneRChannelController自己的颜色作为基准）
    private void SaveOriginalRChannelColors()
    {
        originalRChannelColors.Clear();

        if (sceneComponents == null) return;

        // 现在保存当前控制器的R通道颜色作为基准，而不是从组件中读取
        Color[] currentRColors = new Color[9];
        for (int i = 0; i < 9; i++)
        {
            currentRColors[i] = rChannelColors[i];
        }

        foreach (var component in sceneComponents)
        {
            if (component == null) continue;
            // 为每个组件保存相同的基准颜色
            Color[] originalRColors = new Color[9];
            System.Array.Copy(currentRColors, originalRColors, 9);
            originalRChannelColors[component] = originalRColors;
        }
    }

    // 恢复原始R通道颜色（恢复控制器的原始设置）
    public void RestoreOriginalRChannelColors()
    {
        if (originalRChannelColors.Count == 0) return;

        // 从保存的数据中恢复控制器的R通道颜色
        foreach (var kvp in originalRChannelColors)
        {
            var originalColors = kvp.Value;
            if (originalColors != null && originalColors.Length >= 9)
            {
                // 恢复控制器的R通道颜色
                for (int i = 0; i < 9; i++)
                {
                    rChannelColors[i] = originalColors[i];
                }
                break; // 只需要恢复一次控制器的颜色
            }
        }

        // 应用恢复的颜色到所有组件
        ApplyRChannelColors(false);

        isInPreviewMode = false;
    }

    // 应用R通道颜色到全局Shader属性和材质球
    public void ApplyRChannelColors(bool isPreview = false)
    {
        if (!isPreview)
        {
            // 应用模式：记录Undo操作
#if UNITY_EDITOR
            UnityEditor.Undo.RecordObject(this, "应用R通道统一颜色");
#endif
        }

        // 1. 使用Shader.SetGlobalColor设置全局颜色属性
        for (int i = 0; i < 9 && i < rChannelColors.Length; i++)
        {
            string propertyName = $"_ColorR{i}";
            Shader.SetGlobalColor(propertyName, rChannelColors[i]);
        }

        // 2. 仅在必要时更新场景中的预览材质球（避免频繁查找组件）
        if (!isPreview && sceneComponents != null)
        {
            foreach (var component in sceneComponents)
            {
                if (component == null || component.previewMaterial == null) continue;

                // 应用R通道颜色到预览材质球
                for (int i = 0; i < 9 && i < rChannelColors.Length; i++)
                {
                    string propertyName = $"_ColorR{i}";
                    if (component.previewMaterial.HasProperty(propertyName))
                    {
                        component.previewMaterial.SetColor(propertyName, rChannelColors[i]);
                    }
                }

#if UNITY_EDITOR
                UnityEditor.EditorUtility.SetDirty(component.previewMaterial);
#endif
            }
        }

        if (isPreview)
        {
            isInPreviewMode = true;
        }
        else
        {
            // 应用后更新颜色基准
            SaveOriginalRChannelColors();
            isInPreviewMode = false;
        }
    }

    // 开始预览模式
    public void StartPreview()
    {
        SaveOriginalRChannelColors();
        ApplyRChannelColors(true);
    }

    // 获取指定索引的R通道颜色
    public Color GetRChannelColor(int index)
    {
        if (index >= 0 && index < 9 && rChannelColors != null)
        {
            // 由于显示顺序从小到大，但数组存储顺序是从大到小，需要反转索引
            int reversedIndex = 8 - index; // 将0->8, 1->7, 2->6, ..., 8->0
            return rChannelColors[reversedIndex];
        }
        return Color.red;
    }

    // 设置指定索引的R通道颜色
    public void SetRChannelColor(int index, Color color, bool recordUndo = true)
    {
        if (index >= 0 && index < 9 && rChannelColors != null)
        {
#if UNITY_EDITOR
            // 只在需要时记录undo操作（避免频繁记录）
            if (recordUndo)
            {
                UnityEditor.Undo.RecordObject(this, "设置R通道颜色");
            }
#endif

            // 由于显示顺序从小到大，但数组存储顺序是从大到小，需要反转索引
            int reversedIndex = 8 - index; // 将0->8, 1->7, 2->6, ..., 8->0
            rChannelColors[reversedIndex] = color;

            // 注意：Shader属性名仍然使用逻辑索引，因为Shader中的定义不变
            string propertyName = $"_ColorR{index}";
            Shader.SetGlobalColor(propertyName, color);
            
            // 快速更新已缓存的组件材质球
            if (sceneComponents != null)
            {
                foreach (var component in sceneComponents)
                {
                    if (component != null && component.previewMaterial != null && 
                        component.previewMaterial.HasProperty(propertyName))
                    {
                        component.previewMaterial.SetColor(propertyName, color);
                    }
                }
            }
            
#if UNITY_EDITOR
            // 标记对象已修改
            UnityEditor.EditorUtility.SetDirty(this);
            
            // 强制刷新所有SetVertexColor组件的Inspector界面
            if (sceneComponents != null)
            {
                foreach (var component in sceneComponents)
                {
                    if (component != null)
                    {
                        UnityEditor.EditorUtility.SetDirty(component);
                    }
                }
            }
            
            // 强制重绘Inspector
            UnityEditor.EditorApplication.QueuePlayerLoopUpdate();
#endif
        }
    }

    // 检查是否处于预览模式
    public bool IsInPreviewMode()
    {
        return isInPreviewMode;
    }

    // 获取场景组件数量
    public int GetSceneComponentCount()
    {
        return sceneComponents != null ? sceneComponents.Length : 0;
    }

    // 获取R通道区间值
    public static float GetRSectionValue(int index)
    {
        if (index >= 0 && index < R_SECTION_VALUES.Length)
        {
            return R_SECTION_VALUES[index];
        }
        return 0f;
    }

    // 接收来自其他工具的顶点色分析信息
    public void UpdateEffectiveRValues(HashSet<float> rValues)
    {
        effectiveRValues.Clear();
        if (rValues != null)
        {
            foreach (var value in rValues)
            {
                effectiveRValues.Add(value);
            }
        }
        analysisValid = true;
    }

    // 检查指定R通道值是否在有效区间内
    public bool IsRValueEffective(float targetValue)
    {
        if (!analysisValid) return false;

        const float tolerance = 0.01f;
        foreach (var effectiveValue in effectiveRValues)
        {
            if (Mathf.Abs(effectiveValue - targetValue) <= tolerance)
            {
                return true;
            }
        }
        return false;
    }

    // 获取有效的R通道区间数量
    public int GetEffectiveRValuesCount()
    {
        return analysisValid ? effectiveRValues.Count : 0;
    }

    // 获取有效R通道区间的描述字符串
    public string GetEffectiveRValuesDescription()
    {
        if (!analysisValid || effectiveRValues.Count == 0)
        {
            return "未检测到有效的R通道区间";
        }

        var sortedValues = effectiveRValues.OrderByDescending(v => v);
        return "有效的R通道区间：" + string.Join(", ", sortedValues.Select(v => v.ToString("F4")));
    }

    // 检查场景是否有变化需要重新分析
    public bool NeedsRefresh()
    {
        SetVertexColor[] currentComponents = FindObjectsOfType<SetVertexColor>();
        
        // 检查组件数量是否变化
        if (sceneComponents == null || currentComponents.Length != sceneComponents.Length)
        {
            return true;
        }

        // 检查组件引用是否变化（更高效的检测方法）
        var currentSet = new HashSet<SetVertexColor>(currentComponents);
        var cachedSet = new HashSet<SetVertexColor>(sceneComponents);
        
        // 如果两个集合不相等，说明有组件变化
        if (!currentSet.SetEquals(cachedSet))
        {
            return true;
        }

        return false;
    }

    // 直接对指定组件和材质球应用R通道颜色（用于单独处理）
    public void ApplyRChannelColorsToMaterial(SetVertexColor component, Material material)
    {
        if (component == null || material == null) return;

        // 应用R通道颜色到指定材质球
        for (int i = 0; i < 9 && i < rChannelColors.Length; i++)
        {
            string propertyName = $"_ColorR{i}";
            if (material.HasProperty(propertyName))
            {
                material.SetColor(propertyName, rChannelColors[i]);
            }
        }

#if UNITY_EDITOR
        UnityEditor.EditorUtility.SetDirty(material);
#endif
    }
}