using UnityEngine;
using UnityEditor;
using System.Collections.Generic;
using System.Linq;

[CustomEditor(typeof(SetVertexColor))]
public class SetVertexColorEditor : Editor
{
    private SerializedProperty sectionColorsProperty;

    // 缓存分析结果（RGB三通道）
    private HashSet<float> effectiveRValues = new HashSet<float>();
    private HashSet<float> effectiveGValues = new HashSet<float>();
    private HashSet<float> effectiveBValues = new HashSet<float>();
    private bool analysisValid = false;

    // 用于检测模型变化的缓存
    private Mesh lastAnalyzedMesh = null;
    private int lastMeshVertexCount = 0;
    private int lastMeshInstanceID = 0;

    private void OnEnable()
    {
        // 获取序列化属性
        sectionColorsProperty = serializedObject.FindProperty("sectionColors");

        // 订阅场景变化事件，用于检测模型更新
        UnityEditor.SceneManagement.EditorSceneManager.sceneOpened += OnSceneChanged;
        UnityEditor.SceneManagement.EditorSceneManager.sceneSaved += OnSceneChanged;
        EditorApplication.hierarchyChanged += OnHierarchyChanged;
    }

    private void OnDisable()
    {
        // 取消订阅事件
        UnityEditor.SceneManagement.EditorSceneManager.sceneOpened -= OnSceneChanged;
        UnityEditor.SceneManagement.EditorSceneManager.sceneSaved -= OnSceneChanged;
        EditorApplication.hierarchyChanged -= OnHierarchyChanged;
    }

    private void OnSceneChanged(UnityEngine.SceneManagement.Scene _, UnityEditor.SceneManagement.OpenSceneMode __)
    {
        // 场景变化时强制重新分析
        ForceReanalysis();
    }

    private void OnSceneChanged(UnityEngine.SceneManagement.Scene _)
    {
        // 场景保存时强制重新分析
        ForceReanalysis();
    }

    private void OnHierarchyChanged()
    {
        // 层级变化时强制重新分析（可能有新的mesh或组件变化）
        ForceReanalysis();
    }

    private void ForceReanalysis()
    {
        // 清空缓存，强制下次GUI刷新时重新分析
        lastAnalyzedMesh = null;
        lastMeshVertexCount = 0;
        lastMeshInstanceID = 0;
        analysisValid = false;

        // 强制重绘Inspector
        Repaint();
    }

    public override void OnInspectorGUI()
    {
        serializedObject.Update();

        SetVertexColor setVertexColor = (SetVertexColor)target;

        // 检查模型是否发生变化，如果变化则重新分析
        CheckAndUpdateModelAnalysis(setVertexColor);

        EditorGUILayout.Space();

        // 绘制sectionColors数组（固定大小为27）
        EditorGUILayout.LabelField("分段颜色设置 (RGB三通道，每通道9个区间)", EditorStyles.boldLabel);

        // 确保数组大小为27
        if (sectionColorsProperty.arraySize != 27)
        {
            sectionColorsProperty.arraySize = 27;
        }

        // 定义区间值（从小到大排列）
        float[] sectionValues = {
            0.1f, 0.1889f, 0.2778f, 0.3667f, 0.4556f,
            0.5444f, 0.6333f, 0.7222f, 0.9f
        };

        // 检测颜色变化
        EditorGUI.BeginChangeCheck();

        // 先绘制R通道（只读识别）
        EditorGUILayout.Space();
        EditorGUILayout.LabelField("R通道（识别结果）", EditorStyles.boldLabel);
        
        for (int i = 0; i < 9; i++)
        {
            float targetValue = sectionValues[i];
            
            // 检查这个区间是否有效
            bool isEffective = IsChannelValueEffective(0, targetValue);
            
            string baseLabel = $"{sectionValues[i]:F2}附近";
            
            Color originalColor = GUI.contentColor;
            GUI.contentColor = isEffective ? GUI.contentColor : Color.gray; // 有效为正常颜色，无效为灰色
            EditorGUILayout.LabelField($"R{i}: {baseLabel}");
            GUI.contentColor = originalColor;
        }

        // 绘制GB两个通道（可编辑）
        string[] channelNames = { "G通道", "B通道" };
        string[] channelLabels = { "G", "B" };

        for (int channel = 0; channel < 2; channel++)
        {
            EditorGUILayout.Space();
            EditorGUILayout.LabelField(channelNames[channel], EditorStyles.boldLabel);

            for (int i = 0; i < 9; i++)
            {
                // 注意：channel + 1 是因为我们跳过了R通道（原来的channel 0）
                int colorIndex = (channel + 1) * 9 + i;
                float targetValue = sectionValues[i];

                // 检查这个区间是否有效
                bool isEffective = IsChannelValueEffective(channel + 1, targetValue);

                // 创建标签内容
                string baseLabel = $"{sectionValues[i]:F2}附近";
                GUIContent labelContent = new GUIContent($"{channelLabels[channel]}{i}: {baseLabel}");

                // 根据有效性设置文字颜色
                if (!isEffective)
                {
                    // 保存原始颜色
                    Color originalColor = GUI.contentColor;
                    GUI.contentColor = Color.gray; // 使用灰色表示无效区间

                    EditorGUILayout.PropertyField(
                        sectionColorsProperty.GetArrayElementAtIndex(colorIndex),
                        labelContent
                    );

                    // 恢复原始颜色
                    GUI.contentColor = originalColor;
                }
                else
                {
                    // 有效区间使用正常颜色
                    EditorGUILayout.PropertyField(
                        sectionColorsProperty.GetArrayElementAtIndex(colorIndex),
                        labelContent
                    );
                }
            }
        }

        // 检查是否有颜色变化，如果有则更新预览材质球
        if (EditorGUI.EndChangeCheck())
        {
            serializedObject.ApplyModifiedProperties();
            // 立即更新预览材质球的颜色
            Debug.Log("检测到GB通道颜色变化，正在更新预览材质球...");
            setVertexColor.UpdatePreviewMaterialColorsPublic();
            // 强制重绘场景视图
            UnityEditor.SceneView.RepaintAll();
        }



        // 添加说明
        EditorGUILayout.HelpBox("有效区间规则：\n• 每个通道的区间值：0.1、0.1889、0.2778、0.3667、0.4556、0.5444、0.6333、0.7222、0.9（±0.01范围）\n• 生效条件：只有当某个通道在区间内且其他两个通道为0时，该区间才生效\n• 例如：(0.1, 0, 0) → R0生效；(0.1, 0, 0.1) → 都不生效\n\n功能说明：\n• R通道：只读识别，显示当前模型的有效R通道区间\n• G/B通道：可编辑颜色，灰色文字表示无效区间\n• 如需R通道统一管理，可使用\"场景R通道统一控制器\"", MessageType.Info);

        // 绘制分隔线
        EditorGUILayout.Space();
        EditorGUILayout.LabelField("", GUI.skin.horizontalSlider);
        EditorGUILayout.Space();

        // 绘制材质球管理部分
        EditorGUILayout.LabelField("材质球管理", EditorStyles.boldLabel);

        SetVertexColor vertexColorComponent = (SetVertexColor)target;

        // 显示预览材质球状态
        if (vertexColorComponent.previewMaterial != null)
        {
            EditorGUILayout.LabelField("预览材质球: " + vertexColorComponent.previewMaterial.name + " (临时)");
        }
        else
        {
            EditorGUILayout.LabelField("预览材质球: 未创建");
        }

        EditorGUILayout.Space();

        // 添加说明
        EditorGUILayout.HelpBox("自动材质球管理：挂载脚本时自动创建临时预览材质球，颜色变化将通过Shader实时显示。材质球仅存在于内存中，不会保存到项目资源。", MessageType.Info);

        serializedObject.ApplyModifiedProperties();
    }

    // 检查模型是否发生变化，如果变化则重新分析
    private void CheckAndUpdateModelAnalysis(SetVertexColor component)
    {
        var meshFilter = component.GetComponent<MeshFilter>();
        if (meshFilter == null || meshFilter.sharedMesh == null)
        {
            // 如果没有mesh，清空分析结果
            if (analysisValid)
            {
                effectiveRValues.Clear();
                effectiveGValues.Clear();
                effectiveBValues.Clear();
                analysisValid = false;
                lastAnalyzedMesh = null;
                lastMeshVertexCount = 0;
                lastMeshInstanceID = 0;
            }
            return;
        }

        var currentMesh = meshFilter.sharedMesh;
        int currentVertexCount = currentMesh.vertexCount;
        int currentInstanceID = currentMesh.GetInstanceID();

        // 检查是否需要重新分析：
        // 1. mesh引用发生变化
        // 2. 顶点数量发生变化
        // 3. mesh实例ID发生变化（检测mesh内容更新）
        // 4. 从未分析过
        bool needReanalysis = (lastAnalyzedMesh != currentMesh) ||
                             (lastMeshVertexCount != currentVertexCount) ||
                             (lastMeshInstanceID != currentInstanceID) ||
                             !analysisValid;

        if (needReanalysis)
        {
            AnalyzeModelVertexColors(component);
            lastAnalyzedMesh = currentMesh;
            lastMeshVertexCount = currentVertexCount;
            lastMeshInstanceID = currentInstanceID;
        }
    }

    // 分析当前模型的顶点色，确定有效区间（RGB三通道）
    private void AnalyzeModelVertexColors(SetVertexColor component)
    {
        effectiveRValues.Clear();
        effectiveGValues.Clear();
        effectiveBValues.Clear();
        analysisValid = false;

        var meshFilter = component.GetComponent<MeshFilter>();
        if (meshFilter == null || meshFilter.sharedMesh == null)
        {
            return;
        }

        var mesh = meshFilter.sharedMesh;
        
        // 静默处理读取权限问题，不做任何检查
        Color[] colors;
        try
        {
            colors = mesh.colors;
        }
        catch
        {
            // 捕获所有异常（包括mesh读取权限问题），完全静默处理
            return;
        }

        if (colors == null || colors.Length == 0)
        {
            return;
        }

        // 定义9个区间的中心值（从小到大排列）
        float[] sectionCenterValues = {
            0.1f,      // 区间0
            0.1889f,   // 区间1
            0.2778f,   // 区间2
            0.3667f,   // 区间3
            0.4556f,   // 区间4
            0.5444f,   // 区间5
            0.6333f,   // 区间6
            0.7222f,   // 区间7
            0.9f       // 区间8
        };

        const float tolerance = 0.01f; // 允许±0.01的误差范围

        // 分析每个顶点色，检查RGB三个通道
        // 只有当某个通道在区间内且其他两个通道为0时，该区间才生效
        foreach (var color in colors)
        {
            const float zeroTolerance = 0.01f; // 判断是否为0的容差

            // 检查R通道（只有当G=0且B=0时才生效）
            if (Mathf.Abs(color.g) <= zeroTolerance && Mathf.Abs(color.b) <= zeroTolerance)
            {
                foreach (var centerValue in sectionCenterValues)
                {
                    if (Mathf.Abs(color.r - centerValue) <= tolerance)
                    {
                        effectiveRValues.Add(centerValue);
                        break;
                    }
                }
            }

            // 检查G通道（只有当R=0且B=0时才生效）
            if (Mathf.Abs(color.r) <= zeroTolerance && Mathf.Abs(color.b) <= zeroTolerance)
            {
                foreach (var centerValue in sectionCenterValues)
                {
                    if (Mathf.Abs(color.g - centerValue) <= tolerance)
                    {
                        effectiveGValues.Add(centerValue);
                        break;
                    }
                }
            }

            // 检查B通道（只有当R=0且G=0时才生效）
            if (Mathf.Abs(color.r) <= zeroTolerance && Mathf.Abs(color.g) <= zeroTolerance)
            {
                foreach (var centerValue in sectionCenterValues)
                {
                    if (Mathf.Abs(color.b - centerValue) <= tolerance)
                    {
                        effectiveBValues.Add(centerValue);
                        break;
                    }
                }
            }
        }

        analysisValid = true;
    }

    // 检查指定通道的值是否在有效区间内（基于实际模型的顶点色分析）
    private bool IsChannelValueEffective(int channel, float targetValue)
    {
        if (!analysisValid)
        {
            return false;
        }

        const float tolerance = 0.01f; // 允许±0.01的误差范围

        HashSet<float> effectiveValues = null;
        switch (channel)
        {
            case 0: // R通道
                effectiveValues = effectiveRValues;
                break;
            case 1: // G通道
                effectiveValues = effectiveGValues;
                break;
            case 2: // B通道
                effectiveValues = effectiveBValues;
                break;
            default:
                return false;
        }

        foreach (var effectiveValue in effectiveValues)
        {
            if (Mathf.Abs(effectiveValue - targetValue) <= tolerance)
            {
                return true;
            }
        }

        return false;
    }
}