<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="951db094-cf2f-407f-8b51-43bafebd54b4" name="更改" comment="" />
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="HighlightingSettingsPerFile">
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/273a7cbcc8d7454ea7ae78e07082aa77823600/e8/6d4dfe78/SceneView.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/../../../2021.3.31f1/Editor/Data/CGIncludes/UnityCG.cginc" root0="SKIP_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/../../../2021.3.31f1/Editor/Data/CGIncludes/UnityStandardBRDF.cginc" root0="SKIP_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/../../../2021.3.31f1/Editor/Data/CGIncludes/UnityStandardUtils.cginc" root0="SKIP_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/Assets/Script/AnimatorControllerGenerator.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/Assets/Script/CustomPPV.cs" root0="FORCE_HIGHLIGHTING" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 1
}</component>
  <component name="ProjectId" id="2tcHf11SVubzpSEBsq3iiiNGmMx" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;ModuleVcsDetector.initialDetectionPerformed&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;node.js.detected.package.eslint&quot;: &quot;true&quot;,
    &quot;node.js.detected.package.tslint&quot;: &quot;true&quot;,
    &quot;node.js.selected.package.eslint&quot;: &quot;(autodetect)&quot;,
    &quot;node.js.selected.package.tslint&quot;: &quot;(autodetect)&quot;,
    &quot;nodejs_package_manager_path&quot;: &quot;npm&quot;,
    &quot;settings.editor.selected.configurable&quot;: &quot;com.augmentcode.intellij.settings&quot;,
    &quot;vue.rearranger.settings.migration&quot;: &quot;true&quot;,
    &quot;附加到 Unity 编辑器.附加到 Unity 编辑器.executor&quot;: &quot;Debug&quot;,
    &quot;附加到 Unity 编辑器.附加到 Unity 编辑器并运行.executor&quot;: &quot;Debug&quot;
  }
}</component>
  <component name="RunManager" selected="附加到 Unity 编辑器.附加到 Unity 编辑器">
    <configuration name="启动 Unity" type="RunUnityExe" factoryName="Unity Executable">
      <option name="EXE_PATH" value="G:\2021.3.31f1\Editor\Unity.exe" />
      <option name="PROGRAM_PARAMETERS" value="-projectPath &quot;G:\Work\XRG_SVN\Mini Game&quot; -debugCodeOptimization" />
      <option name="WORKING_DIRECTORY" value="G:\Work\XRG_SVN\Mini Game" />
      <option name="PASS_PARENT_ENVS" value="1" />
      <option name="USE_EXTERNAL_CONSOLE" value="0" />
      <option name="ENV_FILE_PATHS" value="" />
      <option name="REDIRECT_INPUT_PATH" value="" />
      <option name="PTY_MODE" value="Auto" />
      <method v="2" />
    </configuration>
    <configuration name="单元测试(批处理模式)" type="RunUnityExe" factoryName="Unity Executable">
      <option name="EXE_PATH" value="G:\2021.3.31f1\Editor\Unity.exe" />
      <option name="PROGRAM_PARAMETERS" value="-runTests -batchmode -projectPath &quot;G:\Work\XRG_SVN\Mini Game&quot; -testResults Logs/results.xml -testPlatform EditMode -debugCodeOptimization" />
      <option name="WORKING_DIRECTORY" value="G:\Work\XRG_SVN\Mini Game" />
      <option name="PASS_PARENT_ENVS" value="1" />
      <option name="USE_EXTERNAL_CONSOLE" value="0" />
      <option name="ENV_FILE_PATHS" value="" />
      <option name="REDIRECT_INPUT_PATH" value="" />
      <option name="PTY_MODE" value="Auto" />
      <method v="2" />
    </configuration>
    <configuration name="附加到 Unity 编辑器" type="UNITY_DEBUG_RUN_CONFIGURATION" factoryName="Unity Debug" show_console_on_std_err="false" show_console_on_std_out="false" port="50000" address="localhost">
      <option name="allowRunningInParallel" value="false" />
      <option name="listenPortForConnections" value="false" />
      <option name="pid" />
      <option name="projectPathOnTarget" />
      <option name="runtimes">
        <list />
      </option>
      <option name="selectedOptions">
        <list />
      </option>
      <option name="useMixedMode" value="false" />
      <method v="2" />
    </configuration>
    <configuration name="附加到 Unity 编辑器并运行" type="UNITY_DEBUG_RUN_CONFIGURATION" factoryName="UNITY_ATTACH_AND_PLAY" show_console_on_std_err="false" show_console_on_std_out="false" port="50000" address="localhost">
      <option name="allowRunningInParallel" value="false" />
      <option name="listenPortForConnections" value="false" />
      <option name="pid" />
      <option name="projectPathOnTarget" />
      <option name="runtimes">
        <list />
      </option>
      <option name="selectedOptions">
        <list />
      </option>
      <option name="useMixedMode" value="false" />
      <method v="2" />
    </configuration>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="应用程序级" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="默认任务">
      <changelist id="951db094-cf2f-407f-8b51-43bafebd54b4" name="更改" comment="" />
      <created>1740644543995</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1740644543995</updated>
      <workItem from="1740644545732" duration="307000" />
      <workItem from="1740711647227" duration="1416000" />
      <workItem from="1740967576949" duration="4750000" />
      <workItem from="1740973442272" duration="1211000" />
      <workItem from="1741069342765" duration="7644000" />
      <workItem from="1741141682287" duration="7388000" />
      <workItem from="1741245640688" duration="3921000" />
      <workItem from="1741659623547" duration="4233000" />
      <workItem from="1741664503710" duration="13899000" />
      <workItem from="1741743579440" duration="7434000" />
      <workItem from="1741836221151" duration="1444000" />
      <workItem from="1741918982519" duration="4313000" />
      <workItem from="1742194054533" duration="6874000" />
      <workItem from="1742261668425" duration="9468000" />
      <workItem from="1742441746676" duration="4776000" />
      <workItem from="1742783714205" duration="817000" />
      <workItem from="1742866387054" duration="4359000" />
      <workItem from="1742896822188" duration="626000" />
      <workItem from="1742978633876" duration="2338000" />
      <workItem from="1743042402521" duration="4498000" />
      <workItem from="1743145628709" duration="5523000" />
      <workItem from="1743401768516" duration="893000" />
      <workItem from="1743573219308" duration="695000" />
      <workItem from="1743644174878" duration="3205000" />
      <workItem from="1744082023532" duration="3369000" />
      <workItem from="1744162511582" duration="2292000" />
      <workItem from="1744277346620" duration="2412000" />
      <workItem from="1744611082866" duration="3462000" />
      <workItem from="1745309583645" duration="629000" />
      <workItem from="1747278479162" duration="1210000" />
      <workItem from="1747293865741" duration="1439000" />
      <workItem from="1747296176312" duration="217000" />
      <workItem from="1747296407315" duration="131000" />
      <workItem from="1747296789574" duration="467000" />
      <workItem from="1747298226487" duration="399000" />
      <workItem from="1747298645647" duration="378000" />
      <workItem from="1747299059901" duration="3050000" />
      <workItem from="1747358961941" duration="5372000" />
      <workItem from="1747723684670" duration="1103000" />
      <workItem from="1747793544079" duration="1190000" />
      <workItem from="1747794767502" duration="9159000" />
      <workItem from="1748228744660" duration="597000" />
      <workItem from="1748231395749" duration="5841000" />
      <workItem from="1748311469029" duration="8176000" />
      <workItem from="1748416437918" duration="819000" />
      <workItem from="1748600862528" duration="1000" />
      <workItem from="1748919272163" duration="601000" />
      <workItem from="1749177033712" duration="803000" />
      <workItem from="1753413141568" duration="1463000" />
      <workItem from="1753424170985" duration="581000" />
      <workItem from="1753427133953" duration="334000" />
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="UnityProjectConfiguration" hasMinimizedUI="true" />
  <component name="UnityProjectDiscoverer">
    <option name="hasUnityReference" value="true" />
    <option name="unityProject" value="true" />
    <option name="unityProjectFolder" value="true" />
  </component>
  <component name="UnityUnitTestConfiguration" currentTestLauncher="Both" />
  <component name="VcsManagerConfiguration">
    <option name="CLEAR_INITIAL_COMMIT_MESSAGE" value="true" />
  </component>
  <component name="XDebuggerManager">
    <breakpoint-manager>
      <breakpoints>
        <line-breakpoint enabled="true" type="DotNet Breakpoints">
          <url>file://$PROJECT_DIR$/Assets/Script/SceneColorCorrection.cs</url>
          <line>15</line>
          <properties documentPath="G:\Work\XRG_SVN\Mini Game\Assets\Script\SceneColorCorrection.cs" containingFunctionPresentation="方法 'ToggleFilter'">
            <startOffsets>
              <option value="508" />
            </startOffsets>
            <endOffsets>
              <option value="532" />
            </endOffsets>
          </properties>
          <option name="timeStamp" value="26" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="DotNet Breakpoints">
          <url>file://$PROJECT_DIR$/Assets/Script/SceneColorCorrection.cs</url>
          <line>13</line>
          <properties documentPath="G:\Work\XRG_SVN\Mini Game\Assets\Script\SceneColorCorrection.cs" containingFunctionPresentation="方法 'ToggleFilter'">
            <startOffsets>
              <option value="408" />
            </startOffsets>
            <endOffsets>
              <option value="451" />
            </endOffsets>
          </properties>
          <option name="timeStamp" value="27" />
        </line-breakpoint>
        <breakpoint enabled="true" type="DotNet_Exception_Breakpoints">
          <properties exception="System.OperationCanceledException" breakIfHandledByOtherCode="false" displayValue="System.OperationCanceledException" />
          <option name="timeStamp" value="28" />
        </breakpoint>
        <breakpoint enabled="true" type="DotNet_Exception_Breakpoints">
          <properties exception="System.Threading.Tasks.TaskCanceledException" breakIfHandledByOtherCode="false" displayValue="System.Threading.Tasks.TaskCanceledException" />
          <option name="timeStamp" value="29" />
        </breakpoint>
        <breakpoint enabled="true" type="DotNet_Exception_Breakpoints">
          <properties exception="System.Threading.ThreadAbortException" breakIfHandledByOtherCode="false" displayValue="System.Threading.ThreadAbortException" />
          <option name="timeStamp" value="30" />
        </breakpoint>
      </breakpoints>
    </breakpoint-manager>
  </component>
</project>