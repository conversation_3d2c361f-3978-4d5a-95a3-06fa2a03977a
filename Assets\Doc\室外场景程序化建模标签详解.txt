室外场景程序化建模标签详解 (Low Poly风格 - Unity)
基于末日丧尸游戏故事背景的程序化建模标签系统

【游戏背景概述】
本标签系统专为末日丧尸题材的车辆驾驶生存游戏设计。游戏分为六个主要阶段：
1. 逃离都市炼狱 & 初获座驾 (章节1-28)
2. 末日公路狂飙 & 资源争夺 (章节29-88)
3. 深入险境 & 钢铁洪流的对决 (章节89-136)
4. 破碎世界 & 危殆同盟 (章节137-176)
5. 旧世回响 & 智械危机 (章节177-216)
6. 最终地平线 & 人类命运 (章节217-236)

玩家驾驶改装车辆在末世道路上求生、战斗、寻找资源，并探索室内建筑进行搜刮和解谜。

【标签系统说明】
以下标签旨在为程序化生成系统提供足够的信息，以便根据当前游戏阶段、故事主题、破坏程度、丧尸威胁等级等全局参数，智能地选择和放置合适的模块与装饰，营造符合末日氛围的场景环境。
1.0 道路网络 (Road Network)
此分类下的标签主要用于定义道路的物理属性、视觉特征、连接性以及其在末日环境中的状态。

【游戏阶段关联说明】
- 第一阶段(1-28章)：主要涉及城市道路、地下车库、高架桥等都市环境
- 第二阶段(29-88章)：重点为高速公路、乡村道路、荒野小径等开放道路
- 第三阶段(89-136章)：包含地下隧道、军事基地道路、特殊环境道路
- 第四阶段(137-176章)：跨大陆桥梁、异域地形道路、浮空结构
- 第五阶段(177-216章)：AI控制区域道路、高科技设施通道
- 第六阶段(217-236章)：终极设施入口、神秘圣地道路

1.1 类型/层级 (Type/Hierarchy):

【都市逃亡道路 - 第一阶段专用】
•	Road_CityMainStreet_Burning (燃烧的城市主干道 - 主题01)
•	Road_CityMainStreet_VehicleBlocked (车辆堵塞的城市主干道 - 主题01)
•	Road_UndergroundGarage_Spiral (地下车库螺旋车道 - 主题02)
•	Road_UndergroundGarage_Level (地下车库平层车道 - 主题02)
•	Road_Overpass_Elevated_Damaged (受损高架桥路段 - 主题03)
•	Road_Overpass_OnRamp (高架桥匝道 - 主题03) 
•	Road_Tunnel_Mountain_Emergency (山体隧道紧急通道 - 主题04)
•	Road_Tunnel_Subway_Abandoned (废弃地铁隧道 - 主题05)
•	Road_Highway_Checkpoint_Abandoned (废弃高速检查站 - 主题06)
•	Road_IndustrialZone_Loading (工业区装卸道路 - 主题07)
•	Road_Residential_Suburban (郊区住宅街道 - 主题08)
•	Road_Commercial_ShoppingDistrict (商业区购物街 - 主题09)
•	Road_Airport_Service (机场服务道路 - 主题10)
•	Road_Waterfront_Dock (滨水区码头道路 - 主题11)
•	Road_Park_Internal (公园内部道路 - 主题12)
•	Road_Mountain_Winding_Narrow (山区盘山公路 - 主题13)
•	Road_Warehouse_Internal (仓库区内部道路 - 主题14)

【末日公路道路 - 第二阶段专用】
•	Road_Highway_Interstate_Abandoned (废弃州际高速公路 - 主题15-44)
•	Road_Desert_Straight_Endless (沙漠直线公路 - 主题15)
•	Road_Coastal_Scenic_Damaged (受损沿海观光路 - 主题16)
•	Road_Forest_Winding_Overgrown (森林蜿蜒道路 - 主题17)
•	Road_Mountain_Pass_Treacherous (山口险路 - 主题18)
•	Road_Farmland_Rural_Dirt (农田乡村土路 - 主题19)
•	Road_GasStation_Access (加油站出入道路 - 主题20)
•	Road_Settlement_Perimeter (定居点外围道路 - 主题21-22)
•	Road_Military_Base_Secured (军事基地安全道路 - 主题23)
•	Road_PowerPlant_Service (发电厂服务道路 - 主题24)
•	Road_Dam_Maintenance (水坝维护道路 - 主题25)
•	Road_City_Fortified_Approach (方舟城接近道路 - 主题26-28)
•	Road_WeaponTest_Restricted (武器试验场限制道路 - 主题29)
•	Road_Volcano_Observatory_Access (火山观测站通道 - 主题30)
•	Road_Research_Campus_Internal (研究园区内部道路 - 主题31)
•	Road_Desert_BuriedCity_Excavated (沙漠古城挖掘道路 - 主题32)
•	Road_Platform_Research_Offshore (离岸研究平台道路 - 主题33)
•	Road_SpaceElevator_Approach (太空电梯接近道路 - 主题34)
•	Road_Factory_Automated_Clean (自动化工厂洁净道路 - 主题35)
•	Road_TreeCanopy_Elevated (树冠层高架道路 - 主题36)
•	Road_FloatingStructure_SciFi (科幻浮空结构道路 - 主题37)
•	Road_Memorial_Ceremonial (纪念碑仪式道路 - 主题38)
•	Road_Bridge_Megastructure_Partial (巨型桥梁部分路段 - 主题39)
•	Road_SaltFlats_Crystalline (结晶盐碱地道路 - 主题40)
•	Road_FungalForest_Spore (真菌森林孢子道路 - 主题41)
•	Road_Coastal_Shipwreck (海岸沉船区道路 - 主题42)
•	Road_Museum_Exhibition_Internal (博物馆展厅内部道路 - 主题43)
•	Road_Ship_Deck_Massive (巨型船只甲板道路 - 主题44)
•	Road_SeedBank_Secure_Underground (地下种子银行安全通道 - 主题44)

【深入险境道路 - 第三阶段专用】
•	Road_Underground_Metro_Flooded (被淹地下地铁 - 主题45)
•	Road_Underground_Highway_Collapsed (坍塌地下高速 - 主题46)
•	Road_Sewer_MainTunnel (下水道主隧道 - 主题47)
•	Road_Cave_Natural_Carved (天然洞穴车道 - 主题48)
•	Road_Bunker_Military_Reinforced (军事掩体加固道路 - 主题49)
•	Road_PowerPlant_Nuclear_Restricted (核电站限制道路 - 主题50)
•	Road_City_Siege_Defensive (围城防御道路 - 主题51-53)
•	Road_Evacuation_Emergency (紧急疏散道路 - 主题54)
•	Road_Horizon_NewFrontier (新地平线道路 - 主题55)

【破碎世界道路 - 第四阶段专用】
•	Road_Bridge_Continental_Ruined (跨大陆桥废墟 - 主题69)
•	Road_Tundra_Frozen_Permafrost (冻原永久冻土路 - 主题70)
•	Road_FungalForest_Mycelium (真菌森林菌丝路 - 主题71)
•	Road_Coastal_Shipgraveyard (海岸沉船墓地路 - 主题72)
•	Road_FloatingCity_Pontoon (水上都市浮桥路 - 主题73)
•	Road_SkyCity_Fallen_Debris (天空之城坠落路 - 主题74)
•	Road_Polar_IceCave_Tunnel (极地冰洞隧道 - 主题75)
•	Road_CrystalMine_Luminous (发光水晶矿道 - 主题76)
•	Road_MobileFortress_Deck (移动要塞甲板路 - 主题77)
•	Road_SonicWeapon_Resonance (音波武器共振路 - 主题78)
•	Road_AlienCrash_Impact (外星坠毁撞击路 - 主题79)
•	Road_MutantWildlife_Territory (变异动物领地路 - 主题80)
•	Road_Underwater_Volcanic (海底火山路 - 主题81)
•	Road_Airship_Storm_Aerial (风暴飞艇空中路 - 主题82)
•	Road_Dimensional_Rift_Unstable (维度裂隙不稳路 - 主题83)
•	Road_MemoryPalace_Shifting (记忆宫殿变化路 - 主题84)
•	Road_Biosphere_Dome_Internal (生物圈穹顶内路 - 主题85)
•	Road_Junkyard_Mega_Compacted (巨型垃圾场压实路 - 主题86)
•	Road_GeothermalDrill_Platform (地热钻井平台路 - 主题87)

【智械危机道路 - 第五阶段专用】
•	Road_AICore_DataCable_Network (AI核心数据线路 - 主题89)
•	Road_NaniteSwarm_Metallic (纳米集群金属路 - 主题90)
•	Road_SpaceElevator_Debris_Field (太空电梯残骸路 - 主题91)
•	Road_BrainVault_Hidden_Access (缸中之脑隐蔽路 - 主题92)
•	Road_BioWeapon_Containment (生物武器隔离路 - 主题93)
•	Road_QuantumComputer_Anomaly (量子计算机异常路 - 主题94)
•	Road_WeatherControl_Storm (天气控制风暴路 - 主题95)
•	Road_CloningFacility_Sterile (克隆工厂无菌路 - 主题96)
•	Road_DigitalCemetery_Server (数字公墓服务器路 - 主题97)
•	Road_SpaceStation_Orbital_Crash (轨道空间站坠毁路 - 主题98)
•	Road_HiveMind_Neural_Network (蜂巢思维神经路 - 主题99)
•	Road_MaglevLine_Abandoned_Track (废弃磁悬浮轨道 - 主题100)
•	Road_BioLab_Secret_Disguised (秘密生化基地伪装路 - 主题101)
•	Road_HolographicCity_Projection (全息投影城市路 - 主题102)
•	Road_Monastery_Isolated_Ancient (与世隔绝修道院路 - 主题103)
•	Road_OilRigCity_Vertical_Maze (石油钻井城市路 - 主题104)
•	Road_WorldTree_Mega_Branch (世界树巨型枝干路 - 主题105)
•	Road_EternalStorm_Eye_Calm (永恒风暴之眼路 - 主题106)
•	Road_LabyrinthCity_Modular (迷宫之城模块路 - 主题107)
•	Road_ProphetTemple_Forbidden (先知圣殿禁地路 - 主题108)

【最终命运道路 - 第六阶段专用】
•	Road_EdenDome_Paradise_Approach (伊甸园穹顶接近路 - 主题109)
•	Road_ArkLaunch_Final_Countdown (方舟发射最终路 - 主题110)
•	Road_OriginLab_Virus_Source (病毒起源实验室路 - 主题111)
•	Road_WorldEngine_Geocentric (世界引擎地心路 - 主题112)
•	Road_Multiverse_Nexus_Reality (多重宇宙交汇路 - 主题113)
•	Road_Ascension_Energy_Sanctum (飞升能量圣域路 - 主题114)
•	Road_Entropy_Void_Universe (寂灭虚无宇宙路 - 主题115)
•	Road_Creator_Workshop_Stellar (造物者星际工坊路 - 主题116)
•	Road_Choice_Nexus_Abstract (抉择之门抽象路 - 主题117)
•	Road_FinalBattle_Destiny (最终决战命运路 - 主题118)

1.2 材质 (Material):

【基础道路材质】
•	Material_Asphalt_New (新沥青 - 方舟城等高级区域)
•	Material_Asphalt_Worn (磨损沥青 - 一般城市道路)
•	Material_Asphalt_Cracked (龟裂沥青 - 废弃城市道路)
•	Material_Asphalt_Melted (熔化沥青 - 火灾/高温区域)
•	Material_Asphalt_Bloodstained (血迹沥青 - 丧尸袭击现场)

【混凝土材质】
•	Material_Concrete_Smooth (光滑混凝土 - 现代建筑)
•	Material_Concrete_Rough (粗糙混凝土 - 工业设施)
•	Material_Concrete_Reinforced (钢筋混凝土 - 军事设施)
•	Material_Concrete_Cracked_Radiation (辐射龟裂混凝土 - 核电站)
•	Material_Concrete_Bloodstained (血迹混凝土 - 战斗现场)
•	Material_Concrete_Scorched (烧焦混凝土 - 爆炸现场)

【自然材质】
•	Material_Gravel (碎石路 - 乡村道路)
•	Material_Gravel_Contaminated (污染碎石 - 化学泄漏区)
•	Material_Dirt_Packed (压实的土路 - 农村地区)
•	Material_Dirt_Loose (松散的土路 - 荒废区域)
•	Material_Dirt_Muddy (泥泞土路 - 雨后/沼泽)
•	Material_Dirt_Radioactive (放射性土壤 - 核污染区)
•	Material_Sand_Desert (沙漠沙土 - 沙漠公路)
•	Material_Sand_Contaminated (污染沙土 - 化学武器试验场)

【特殊末日材质】
•	Material_Cobblestone_Ancient (古代鹅卵石 - 历史遗迹)
•	Material_Mud_Toxic (有毒泥浆 - 化学污染区)
•	Material_SteelPlate_Temporary (临时钢板路面 - 紧急修复)
•	Material_SteelPlate_Rusted (锈蚀钢板 - 废弃工业区)
•	Material_Metal_SteelDeck (金属甲板 - 船只/平台)
•	Material_Metal_Grated (金属格栅 - 工业走道)
•	Material_Metal_Corrugated (波纹金属板 - 临时建筑)

【有机/变异材质】
•	Material_Wood_LivingTree (活体树木 - 巨型树枝路径)
•	Material_Wood_Rotting (腐烂木材 - 废弃木桥)
•	Material_Flesh_Organic (有机血肉 - 病毒感染区)
•	Material_Fungal_Mycelium (真菌菌丝 - 真菌森林)
•	Material_Crystal_Growing (生长水晶 - 水晶矿洞)
•	Material_Crystal_Radioactive (放射性水晶 - 异常区域)

【高科技材质】
•	Material_ScrapMetal_RoadSurface (废金属路面 - 垃圾场)
•	Material_ScrapMetal_Welded (焊接废金属 - 拾荒者建造)
•	Material_Composite_SciFi (科幻复合材料 - 高科技设施)
•	Material_Energy_Solidified (固化能量 - 超自然区域)
•	Material_Nanite_SelfRepairing (自修复纳米材料 - AI控制区)
•	Material_Holographic_Projection (全息投影材料 - 虚拟城市)

【冰雪/极地材质】
•	Material_Ice_Thick (厚冰层 - 冰封河道)
•	Material_Ice_Cracked (裂冰 - 不稳定冰面)
•	Material_Snow_Packed (压实雪 - 极地道路)
•	Material_Snow_Contaminated (污染雪 - 核冬天)
•	Material_Permafrost (永久冻土 - 极地地区)

【水体/液体材质】
•	Material_Water_Shallow (浅水 - 被淹道路)
•	Material_Water_Toxic (有毒积水 - 化学污染)
•	Material_Oil_Spilled (溢油 - 工业事故)
•	Material_Acid_Corrosive (腐蚀性酸液 - 化学武器)
•	Material_Blood_Coagulated (凝固血液 - 大屠杀现场)

【特殊环境材质】
•	Material_Ash_Volcanic (火山灰 - 火山区域)
•	Material_Ash_Nuclear (核灰 - 核爆现场)
•	Material_Glass_Melted (熔化玻璃 - 高温爆炸)
•	Material_Debris_Mixed (混合残骸 - 爆炸废墟)
•	Material_Bone_Crushed (碎骨 - 丧尸聚集地)
•	Material_Slime_Toxic (有毒粘液 - 变异生物分泌)

1.3 宽度/车道 (Width/Lanes):

【标准车道配置】
•	Width_SingleLane (单车道 - 乡村小路)
•	Width_DoubleLane (双车道 - 一般公路)
•	Width_MultiLane_Three (三车道 - 城市主干道)
•	Width_MultiLane_FourPlus (四车道及以上 - 高速公路)
•	Width_Narrow (狭窄，< 标准单车道 - 小巷/应急通道)
•	Width_Wide (宽阔，> 标准多车道 - 机场跑道)

【末日特殊宽度】
•	Width_Variable_DueToDamage (因损坏导致宽度可变 - 爆炸/坍塌)
•	Width_Variable_DueToDamage_Extreme (因极端损坏导致宽度多变 - 战争废墟)
•	Width_Blocked_Partial (部分阻塞 - 废弃车辆/路障)
•	Width_Blocked_Severe (严重阻塞 - 大量障碍物)
•	Width_Expanded_Emergency (紧急拓宽 - 疏散路线)
•	Width_Compressed_Defensive (防御性收窄 - 检查站/要塞入口)

1.4 状况/状态 (Condition/State):

【基础道路状态】
•	State_Intact (完好 - 方舟城等高级区域)
•	State_Intact_Clean (完好且洁净 - 方舟城内部)
•	State_Intact_Pristine (完好无损且原始 - AI工厂)
•	State_Weathered_Light (轻微风化 - 一般废弃道路)
•	State_Weathered_Heavy (严重风化 - 长期废弃)

【裂纹/破损状态】
•	State_Cracked_Minor (轻微龟裂 - 老化道路)
•	State_Cracked_Major (严重龟裂 - 地震/爆炸影响)
•	State_Cracked_Extreme_Bridge (桥面极端破裂 - 结构失效)
•	State_Cracked_IceFissure_Deep (深邃冰裂缝 - 极地环境)
•	State_Potholed (有坑洞 - 维护不良)
•	State_Potholed_Extreme_Craters (大量巨型弹坑 - 战争区域)
•	State_Potholed_Massive_BridgeGap (桥面巨大坑洞/断裂 - 桥梁损毁)

【地形变化状态】
•	State_Uneven (不平整/隆起 - 地质活动)
•	State_Uneven_VolcanicActivity (火山活动导致不平整 - 火山区)
•	State_Sloped_Extreme_Ruin (废墟中极度倾斜的路面 - 建筑坍塌)
•	State_Sinking_Subsidence (下沉/塌陷 - 地基不稳)
•	State_Uplifted_Geological (地质抬升 - 地震后果)

【植被覆盖状态】
•	State_Overgrown_Edges (边缘长草 - 轻度荒废)
•	State_Overgrown_Full (大面积长草/被植被覆盖 - 重度荒废)
•	State_Overgrown_MutatedFlora (变异植物覆盖 - 生物污染区)
•	State_Overgrown_Fungal (真菌覆盖 - 真菌感染区)

【水体影响状态】
•	State_Flooded_Shallow (浅积水 - 排水不良)
•	State_Flooded_Deep (深积水 - 严重水灾)
•	State_Flooded_Tidal_Intermittent (潮汐间歇性淹没 - 海岸道路)
•	State_Muddy_DueToRain (雨后泥泞 - 乡村土路)
•	State_WaterDamaged_Erosion (水蚀损坏 - 长期浸泡)

【冰雪/极地状态】
•	State_Icy (结冰 - 冬季/极地)
•	State_Icy_ExtremeSlickness (极度湿滑结冰 - 冰河路)
•	State_SnowCovered (积雪覆盖 - 冬季道路)
•	State_SnowCovered_Heavy (厚雪覆盖 - 暴雪后)
•	State_FrostCovered (覆盖霜雪 - 极地建筑)
•	State_Frozen_Permafrost (永久冻结 - 极地地区)

【污染/化学状态】
•	State_Sandy (积沙覆盖 - 沙漠道路)
•	State_SandCovered_Partial (部分被沙掩埋 - 沙漠建筑)
•	State_RadiationDustCovered (覆盖放射性尘埃 - 核污染区)
•	State_Corroded_Chemical (化学腐蚀 - 工业区)
•	State_Contaminated_Biological (生物污染 - 病毒泄漏区)
•	State_Contaminated_Toxic (有毒污染 - 化学武器区)

【火灾/爆炸状态】
•	State_Burnt (燃烧痕迹 - 火灾现场)
•	State_Burnt_WidespreadFires (大面积燃烧 - 逃离场景)
•	State_Scorched_Nuclear (核爆烧焦 - 核爆现场)
•	State_Melted_HighHeat (高温熔化 - 火山/爆炸区)
•	State_Vitrified_WeaponTest (玻璃化 - 武器试验场)

【战争/暴力状态】
•	State_Bloodstained (血迹斑斑 - 战斗现场)
•	State_Bloodstained_Massive (大面积血迹 - 大屠杀现场)
•	State_ImpactDamage_Vehicle (车辆撞击损坏 - 交通事故)
•	State_ImpactDamage_Weapon (武器撞击损坏 - 战争废墟)
•	State_Crushed_ByMegaVehicle (被巨型载具碾压变形 - 重型机械)
•	State_Shelled_Artillery (炮击损坏 - 战争区域)

【阻塞/障碍状态】
•	State_RubbleCovered (覆盖瓦砾 - 建筑坍塌)
•	State_Blocked_Vehicles_Heavy (被大量车辆严重堵塞 - 交通瘫痪)
•	State_Blocked_Vehicles_Partial (被部分车辆堵塞 - 轻度拥堵)
•	State_Blocked_Barricades_Heavy (被重型路障堵塞 - 军事封锁)
•	State_Blocked_Debris_Massive (被大量残骸堵塞 - 爆炸后果)
•	State_Landslide_Blocked (塌方堵塞 - 盘山公路)

【结构失效状态】
•	State_Collapsed_Partial_Tunnel (隧道部分塌方 - 结构失效)
•	State_Collapsing_Road (道路正在崩塌 - 逃离场景)
•	State_Bridge_MisalignedJoint (桥梁连接处错位 - 地震影响)
•	State_Bridge_CollapsedSection (桥梁部分断裂/坍塌 - 结构失效)
•	State_Tunnel_VentilationFailure (隧道通风失效 - 系统故障)

【特殊末日状态】
•	State_OilStained (有油污 - 加油站，修车厂)
•	State_Surface_OilySlick_PlatformDeck (平台甲板油腻湿滑 - 工业平台)
•	State_Track_Broken_Twisted_Maglev (磁悬浮轨道断裂扭曲 - 高科技废墟)
•	State_Damage_NaniteDecomposition_Partial (纳米机器人部分分解 - AI区域)
•	State_Damage_Heavy_StructuralCollapse_Mega (巨型结构严重坍塌 - 太空电梯)
•	State_Building_Mobile_Modular (模块化可移动建筑 - 移动城市)

1.5 特征/组件 (Features/Components):

【基础道路设施】
•	Feature_Sidewalk_Left (左侧人行道 - 城市街道)
•	Feature_Sidewalk_Right (右侧人行道 - 城市街道)
•	Feature_Sidewalk_Both (两侧人行道 - 主要街道)
•	Feature_NoSidewalk (无人行道 - 乡村道路)
•	Feature_Curb_High (高路缘 - 城市道路)
•	Feature_Curb_Low (低路缘 - 住宅区)
•	Feature_Curb_Damaged (损坏路缘 - 废弃道路)
•	Feature_Curb_Bloodstained (血迹路缘 - 丧尸袭击现场)

【护栏/隔离设施】
•	Feature_Guardrail_Metal (金属护栏 - 高速公路)
•	Feature_Guardrail_Concrete (混凝土护栏 - 桥梁)
•	Feature_Guardrail_Damaged (损坏护栏 - 事故现场)
•	Feature_Guardrail_Bloodstained (血迹护栏 - 丧尸攻击)
•	Feature_MedianStrip_Grass (中央草地隔离带 - 城市主干道)
•	Feature_MedianStrip_Concrete (中央混凝土隔离带 - 高速公路)
•	Feature_MedianStrip_Overgrown (杂草丛生隔离带 - 废弃道路)
•	Feature_MedianStrip_Destroyed (被毁隔离带 - 战争废墟)

【交叉路口】
•	Feature_Intersection_Cross (十字交叉口 - 城市街道)
•	Feature_Intersection_T (T字交叉口 - 支路连接)
•	Feature_Intersection_Y (Y字交叉口 - 分岔路)
•	Feature_Intersection_Roundabout (环岛 - 交通枢纽)
•	Feature_Intersection_Damaged (损坏交叉口 - 爆炸/坍塌)
•	Feature_Intersection_Blocked (阻塞交叉口 - 路障/废车)

【隧道/桥梁设施】
•	Feature_Tunnel_Entrance (隧道入口 - 山体/地下)
•	Feature_Tunnel_Exit (隧道出口 - 山体/地下)
•	Feature_Tunnel_Entrance_Collapsed (坍塌隧道入口 - 结构失效)
•	Feature_Tunnel_Emergency_Exit (隧道紧急出口 - 安全设施)
•	Feature_Bridge_Approach (桥梁引桥 - 接近段)
•	Feature_Bridge_Span (桥梁主体 - 跨越段)
•	Feature_Bridge_Span_CollapsedSections (桥梁主体垮塌段 - 结构损毁)
•	Feature_Bridge_Pier_Damaged (损坏桥墩 - 结构问题)

【排水/维护设施】
•	Feature_DrainageGrate (排水格栅 - 城市道路)
•	Feature_DrainageGrate_Clogged (堵塞排水格栅 - 维护不良)
•	Feature_DrainageGrate_Broken (破损排水格栅 - 损坏设施)
•	Feature_ManholeCover (窨井盖 - 地下设施入口)
•	Feature_ManholeCover_Missing (丢失窨井盖 - 危险区域)
•	Feature_ManholeCover_Bloodstained (血迹窨井盖 - 丧尸藏身处)

【军事/防御设施】
•	Feature_Roadblock_Military_Sandbags (军事路障 - 沙袋 - 检查站)
•	Feature_Roadblock_Military_Concrete (军事路障 - 混凝土块 - 防御工事)
•	Feature_Roadblock_Makeshift_Containers (临时路障 - 集装箱 - 紧急封锁)
•	Feature_Roadblock_Abandoned (废弃路障 - 失效防线)
•	Feature_Checkpoint_Abandoned (废弃检查站 - 军事撤离)
•	Feature_Minefield_MarkedOrUnmarked (雷区 - 有或无标记 - 军事禁区)
•	Feature_TankTrap_Concrete (混凝土反坦克障碍 - 防御工事)
•	Feature_BarbedWire_Coil (蛇腹形带刺铁丝网 - 军事封锁)

【交通控制设施】
•	Feature_TrafficLight_Working (正常交通信号灯 - 方舟城等)
•	Feature_TrafficLight_Damaged (损坏交通信号灯 - 废弃城市)
•	Feature_TrafficLight_Flickering (闪烁交通信号灯 - 电力不稳)
•	Feature_TrafficLight_Dark (熄灭交通信号灯 - 停电区域)
•	Feature_StopSign_Intact (完好停止标志 - 乡村路口)
•	Feature_StopSign_Damaged (损坏停止标志 - 废弃路口)
•	Feature_SpeedBump_Asphalt (沥青减速带 - 住宅区)
•	Feature_SpeedBump_Damaged (损坏减速带 - 废弃道路)

【服务设施】
•	Feature_TollBooth_Ruined (收费站废墟 - 高速公路)
•	Feature_ServiceArea_Highway_Abandoned (废弃高速服务区 - 补给点)
•	Feature_RestStop_Abandoned (废弃休息站 - 旅行者遗迹)
•	Feature_WeighStation_Abandoned (废弃称重站 - 货运检查)
•	Feature_GasStation_Canopy (加油站遮雨棚 - 燃料补给)
•	Feature_GasStation_Pumps_Destroyed (被毁加油泵 - 爆炸现场)

【铁路交叉设施】
•	Feature_RailwayCrossing_Road (铁路公路交叉口 - 危险路段)
•	Feature_RailwayCrossing_Damaged (损坏铁路交叉口 - 事故现场)
•	Feature_RailwayTrack_Tunnel (隧道内铁轨 - 地下交通)
•	Feature_RailwayTrack_Monorail_Elevated (高架单轨铁路 - 方舟城)
•	Feature_RailwayTrack_Maglev_Elevated_Ruined (废弃高架磁悬浮轨道 - 高科技废墟)

【特殊末日设施】
•	Feature_OverheadGantrySign_Highway (高速公路龙门架指示牌 - 导航)
•	Feature_OverheadGantrySign_Damaged (损坏龙门架 - 废弃高速)
•	Feature_EmergencyRamp_Steep (紧急避险坡道 - 盘山路/高速)
•	Feature_ReflectiveRoadStud (路面反光道钉 - 夜间导航)
•	Feature_ReflectiveRoadStud_Missing (缺失反光道钉 - 维护不良)
•	Feature_Walkway_Maintenance_Tunnel (隧道维修通道 - 工程设施)

【高科技/未来设施】
•	Feature_ElevatorPlatform_Vehicle_Large (大型车辆升降平台 - 方舟城入口)
•	Feature_TestPlatform_Outdoor_Scorched (露天试验平台 - 烧灼 - 武器试验)
•	Feature_Helipad_Platform (平台直升机场 - 空中交通)
•	Feature_Helipad_Damaged (损坏直升机场 - 废弃设施)
•	Feature_LaunchPad_Rocket (火箭发射台 - 航天设施)
•	Feature_LaunchPad_Abandoned (废弃发射台 - 太空计划遗迹)

【海上/水上设施】
•	Feature_Walkway_OilRig_InterConnector_Bridge (石油钻井平台连接桥 - 海上设施)
•	Feature_Cableway_OilRig_InterConnector (石油钻井平台间缆索 - 运输系统)
•	Feature_Dock_Loading (装卸码头 - 货物运输)
•	Feature_Dock_Damaged (损坏码头 - 海岸废墟)
•	Feature_Pier_Fishing (渔业码头 - 海岸生活)
•	Feature_Pier_Collapsed (坍塌码头 - 结构失效)

【生存者改造设施】
•	Feature_Barricade_Improvised (临时路障 - 生存者建造)
•	Feature_Watchtower_Makeshift (临时瞭望塔 - 防御哨所)
•	Feature_Spikes_AntiVehicle (反车辆尖刺 - 防御工事)
•	Feature_TireWall_Defensive (轮胎防御墙 - 废料利用)
•	Feature_ScrapMetal_Barrier (废金属屏障 - 拾荒者建造)
•	Feature_Sandbag_Fortification (沙袋工事 - 临时防御)

1.6 标线 (Markings):

【基础道路标线】
•	Marking_CenterLine_Solid_Single (单实中线 - 标准道路)
•	Marking_CenterLine_Solid_Double (双实中线 - 禁止超车)
•	Marking_CenterLine_Dashed_Single (单虚中线 - 允许超车)
•	Marking_CenterLine_SolidDashed (一实一虚中线 - 单向超车)
•	Marking_LaneLine_Solid (车道实线 - 禁止变道)
•	Marking_LaneLine_Dashed (车道虚线 - 允许变道)
•	Marking_EdgeLine_Solid (边缘实线 - 道路边界)

【交通标线】
•	Marking_PedestrianCrossing (人行横道线 - 行人通道)
•	Marking_PedestrianCrossing_Faded (褪色人行横道 - 废弃道路)
•	Marking_PedestrianCrossing_Bloodstained (血迹人行横道 - 丧尸袭击现场)
•	Marking_StopLine (停止线 - 交叉路口)
•	Marking_StopLine_Worn (磨损停止线 - 老化道路)
•	Marking_YieldLine (让行线 - 支路汇入)

【方向指示标线】
•	Marking_Arrow_Straight (直行箭头 - 车道指示)
•	Marking_Arrow_Turn_Left (左转箭头 - 转向车道)
•	Marking_Arrow_Turn_Right (右转箭头 - 转向车道)
•	Marking_Arrow_UTurn (掉头箭头 - 掉头车道)
•	Marking_Arrow_Multiple (多方向箭头 - 复合车道)

【特殊标线】
•	Marking_ParkingSpaceLines (停车位标线 - 地下车库/停车场)
•	Marking_ParkingSpaceLines_Faded (褪色停车位标线 - 废弃停车场)
•	Marking_LoadingZone (装卸区标线 - 货运区域)
•	Marking_BusLane (公交专用道标线 - 城市交通)
•	Marking_BikeLane (自行车道标线 - 绿色出行)
•	Marking_Handicap (残疾人停车标线 - 无障碍设施)

【末日特殊标线】
•	Marking_NoMarkings (无标线 - 乡村道路/废弃区域)
•	Marking_Faded (标线褪色 - 长期无维护)
•	Marking_Faded_Severe (严重褪色 - 几乎不可见)
•	Marking_Bloodstained (血迹覆盖标线 - 丧尸袭击现场)
•	Marking_Burned (烧毁标线 - 火灾现场)
•	Marking_Scraped_Off (刮除标线 - 人为破坏)
•	Marking_Covered_Debris (残骸覆盖标线 - 爆炸现场)

【临时/紧急标线】
•	Marking_Temporary_Paint (临时油漆标线 - 应急修复)
•	Marking_Chalk_Makeshift (粉笔临时标记 - 生存者标识)
•	Marking_Spray_Graffiti (喷漆涂鸦 - 帮派标记)
•	Marking_Blood_Trail (血迹痕迹 - 拖拽尸体)
•	Marking_Tire_Skid (轮胎刹车痕 - 紧急制动)
•	Marking_Explosion_Scorch (爆炸烧焦痕迹 - 战斗现场)

【高科技标线】
•	Marking_LED_Embedded (嵌入式LED标线 - 方舟城高科技道路)
•	Marking_Holographic_Projection (全息投影标线 - 虚拟城市)
•	Marking_Energy_Glowing (发光能量标线 - 超自然区域)
•	Marking_Nanite_SelfRepairing (纳米自修复标线 - AI控制区)

1.7 交通流向 (TrafficFlow - 逻辑标签):

【基础流向】
•	Flow_OneWay (单向 - 城市单行道)
•	Flow_TwoWay (双向 - 标准公路)
•	Flow_Reversible (可逆车道 - 潮汐车道)
•	Flow_Blocked (阻断 - 路障封锁)
•	Flow_Restricted (限制通行 - 军事管制)

【末日特殊流向】
•	Flow_Evacuation_OneWay (单向疏散 - 紧急撤离)
•	Flow_Contraflow_Emergency (逆向应急 - 灾难疏散)
•	Flow_Patrol_Military (军事巡逻 - 戒严状态)
•	Flow_Survivor_Convoy (生存者车队 - 集体行动)
•	Flow_Zombie_Horde (丧尸群移动 - 威胁区域)
•	Flow_Abandoned_NoTraffic (废弃无交通 - 死寂区域)

1.8 环境上下文 (EnvironmentContext - 逻辑标签):

【第一阶段：都市逃亡环境】
•	Context_Urban_Core_Burning (燃烧的城市核心区 - 主题01)
•	Context_Urban_Core_Abandoned (废弃城市核心区 - 主题01)
•	Context_Underground_Garage_Flooded (被淹地下车库 - 主题02)
•	Context_Underground_Garage_Emergency (紧急地下车库 - 主题02)
•	Context_Overpass_Damaged_Structural (结构损坏高架桥 - 主题03)
•	Context_Overpass_Collapsed_Partial (部分坍塌高架桥 - 主题03)
•	Context_Tunnel_Mountain_Blocked (山体隧道阻塞 - 主题04)
•	Context_Tunnel_Emergency_Escape (隧道紧急逃生 - 主题04)
•	Context_Subway_Abandoned_Dark (废弃黑暗地铁 - 主题05)
•	Context_Subway_Flooded_Dangerous (危险被淹地铁 - 主题05)
•	Context_Highway_Checkpoint_Military (高速军事检查站 - 主题06)
•	Context_Highway_Checkpoint_Overrun (被攻陷检查站 - 主题06)
•	Context_Industrial_Loading_Dock (工业装卸码头 - 主题07)
•	Context_Industrial_Warehouse_Maze (工业仓库迷宫 - 主题07)
•	Context_Residential_Suburban_Quiet (安静郊区住宅 - 主题08)
•	Context_Residential_Suburban_Chaos (混乱郊区住宅 - 主题08)
•	Context_Commercial_Shopping_Looted (被洗劫购物区 - 主题09)
•	Context_Commercial_Shopping_Barricaded (路障购物区 - 主题09)
•	Context_Airport_Service_Abandoned (废弃机场服务区 - 主题10)
•	Context_Airport_Runway_Emergency (机场跑道紧急区 - 主题10)
•	Context_Waterfront_Dock_Evacuation (滨水码头疏散区 - 主题11)
•	Context_Waterfront_Marina_Abandoned (废弃游艇码头 - 主题11)
•	Context_Park_Urban_Overgrown (城市公园杂草丛生 - 主题12)
•	Context_Park_Refugee_Camp (公园难民营 - 主题12)
•	Context_Mountain_Road_Treacherous (山路险峻路段 - 主题13)
•	Context_Mountain_Road_Landslide (山路塌方路段 - 主题13)
•	Context_Warehouse_Logistics_Hub (物流仓库枢纽 - 主题14)
•	Context_Warehouse_Storage_Maze (仓储迷宫区域 - 主题14)

【第二阶段：末日公路环境】
•	Context_Desert_Highway_Endless (无尽沙漠高速 - 主题15)
•	Context_Desert_Oasis_Mirage (沙漠绿洲幻象 - 主题15)
•	Context_Coastal_Scenic_Storm (暴风雨海岸路 - 主题16)
•	Context_Coastal_Cliff_Dangerous (危险海岸悬崖 - 主题16)
•	Context_Forest_Dense_Overgrown (茂密森林道路 - 主题17)
•	Context_Forest_Logging_Abandoned (废弃伐木区 - 主题17)
•	Context_Mountain_Pass_Snowy (雪山山口通道 - 主题18)
•	Context_Mountain_Peak_Observatory (山顶观测站 - 主题18)
•	Context_Farmland_Abandoned_Crops (废弃农田作物 - 主题19)
•	Context_Farmland_Barn_Shelter (农场谷仓避难所 - 主题19)
•	Context_GasStation_Highway_Last (高速最后加油站 - 主题20)
•	Context_GasStation_Explosion_Site (加油站爆炸现场 - 主题20)

【生存者定居点环境】
•	Context_Settlement_Scavenger_Fortified (拾荒者要塞定居点 - 主题21)
•	Context_Settlement_Trader_Hub (商人贸易枢纽 - 主题22)
•	Context_Settlement_Survivor_Community (生存者社区 - 主题22)
•	Context_Settlement_Abandoned_Ghost (废弃幽灵定居点 - 主题22)

【军事/政府设施环境】
•	Context_Military_Base_Secured (安全军事基地 - 主题23)
•	Context_Military_Base_Overrun (被攻陷军事基地 - 主题23)
•	Context_PowerPlant_Nuclear_Active (运行中核电站 - 主题24)
•	Context_PowerPlant_Nuclear_Meltdown (核电站熔毁 - 主题24)
•	Context_Dam_Hydroelectric_Functional (功能性水电站 - 主题25)
•	Context_Dam_Structural_Failure (大坝结构失效 - 主题25)

【方舟城环境】
•	Context_ArkCity_Approach_Checkpoint (方舟城接近检查站 - 主题26)
•	Context_ArkCity_Outer_Defenses (方舟城外围防御 - 主题26)
•	Context_ArkCity_Inner_Sanctuary (方舟城内部圣域 - 主题27)
•	Context_ArkCity_Underground_Network (方舟城地下网络 - 主题28)

【特殊试验环境】
•	Context_WeaponTest_Desert_Range (沙漠武器试验场 - 主题29)
•	Context_WeaponTest_Underground_Bunker (地下武器试验掩体 - 主题29)
•	Context_Volcano_Observatory_Active (活火山观测站 - 主题30)
•	Context_Volcano_Evacuation_Zone (火山疏散区 - 主题30)

【研究设施环境】
•	Context_Research_Campus_Pharmaceutical (制药研究园区 - 主题31)
•	Context_Research_Lab_Abandoned (废弃研究实验室 - 主题31)
•	Context_Desert_BuriedCity_Archaeological (沙漠古城考古区 - 主题32)
•	Context_Desert_Sandstorm_Hazard (沙漠沙尘暴危险区 - 主题32)

【高科技设施环境】
•	Context_Platform_Research_Offshore (离岸研究平台 - 主题33)
•	Context_Platform_Oil_Drilling (海上石油钻井平台 - 主题33)
•	Context_SpaceElevator_Base_Operational (运行中太空电梯基地 - 主题34)
•	Context_SpaceElevator_Debris_Field (太空电梯残骸场 - 主题34)
•	Context_Factory_AI_Automated (AI自动化工厂 - 主题35)
•	Context_Factory_Production_Sterile (无菌生产工厂 - 主题35)

【自然奇观环境】
•	Context_TreeCanopy_Giant_Living (巨型活体树冠 - 主题36)
•	Context_TreeCanopy_Village_Primitive (树冠原始村落 - 主题36)
•	Context_FloatingStructure_SciFi_Ruins (科幻浮空结构废墟 - 主题37)
•	Context_FloatingStructure_Energy_Anomaly (浮空结构能量异常 - 主题37)

【纪念与希望环境】
•	Context_Memorial_Disaster_Monument (灾难纪念碑 - 主题38)
•	Context_Memorial_Hope_Beacon (希望灯塔 - 主题38)
•	Context_Settlement_New_Hopeful (充满希望的新定居点 - 主题38)

【巨型结构环境】
•	Context_Bridge_Megastructure_Intact (完整巨型桥梁 - 主题39)
•	Context_Bridge_Megastructure_Collapsed (坍塌巨型桥梁 - 主题39)
•	Context_SaltFlats_Crystalline_Vast (广阔结晶盐碱地 - 主题40)
•	Context_SaltFlats_Mining_Abandoned (废弃盐矿开采区 - 主题40)

【异常生态环境】
•	Context_FungalForest_Spore_Toxic (有毒孢子真菌森林 - 主题41)
•	Context_FungalForest_Bioluminescent (生物发光真菌森林 - 主题41)
•	Context_Coastal_Shipgraveyard_Vast (广阔海岸沉船墓地 - 主题42)
•	Context_Coastal_Lighthouse_Ruined (废弃海岸灯塔 - 主题42)

【文化设施环境】
•	Context_Museum_Exhibition_Preserved (保存完好的博物馆展厅 - 主题43)
•	Context_Museum_Looted_Vandalized (被洗劫破坏的博物馆 - 主题43)
•	Context_Ship_Cruise_Beached (搁浅游轮 - 主题44)
•	Context_Ship_Military_Carrier (军用航空母舰 - 主题44)

【种子银行环境】
•	Context_SeedBank_Underground_Secure (地下安全种子银行 - 主题44)
•	Context_SeedBank_Genetic_Archive (基因档案库 - 主题44)

【总结说明】
以上标签系统为末日丧尸游戏的室外场景程序化建模提供了全面的分类体系。每个标签都与具体的游戏章节主题相关联，确保生成的场景能够准确反映当前的故事进展和氛围需求。

程序化生成系统可以根据当前游戏阶段、章节主题、威胁等级等参数，智能选择合适的道路类型、材质、状态、特征等标签组合，创造出符合末日氛围且具有叙事意义的道路网络环境。

【使用建议】
1. 根据当前章节主题选择对应的道路类型和环境上下文
2. 结合游戏进度调整道路状态和损坏程度
3. 考虑丧尸威胁等级来配置防御设施和路障
4. 利用材质和标线的组合来营造不同的视觉效果
5. 通过特征组件的搭配来增加场景的细节和真实感

此标签系统将为玩家提供丰富多样、符合剧情发展的道路驾驶体验，增强游戏的沉浸感和叙事表现力。

========================================
2.0 自然类 (Natural Elements)
========================================

此分类涵盖所有自然环境元素，包括地形特征、植被、岩石、水体等，为末日世界提供基础的自然背景。

2.1 地形特征 (Terrain Features)

2.1.1 基础地形类型 (Basic Terrain Types):
• Terrain_Plains_Flat (平原 - 平坦)
• Terrain_Plains_Rolling (平原 - 起伏)
• Terrain_Hills_Gentle (丘陵 - 缓坡)
• Terrain_Hills_Steep (丘陵 - 陡坡)
• Terrain_Mountains_Low (低山)
• Terrain_Mountains_High (高山)
• Terrain_Valley_Narrow (狭谷)
• Terrain_Valley_Wide (宽谷)
• Terrain_Plateau_Elevated (高原台地)
• Terrain_Basin_Depression (盆地洼地)
• Terrain_Ridge_Sharp (尖锐山脊)
• Terrain_Ridge_Rounded (圆润山脊)
• Terrain_Escarpment_Cliff (悬崖峭壁)
• Terrain_Mesa_FlatTop (平顶山)
• Terrain_Butte_Isolated (孤立丘)
• Terrain_Canyon_Deep (深峡谷)
• Terrain_Canyon_Shallow (浅峡谷)
• Terrain_Gorge_Narrow (狭窄峡谷)
• Terrain_Ravine_Eroded (侵蚀沟壑)
• Terrain_Gully_Small (小冲沟)

2.1.2 特殊地形特征 (Special Terrain Features):
• Feature_Cave_Entrance_Small (小型洞穴入口)
• Feature_Cave_Entrance_Large (大型洞穴入口)
• Feature_Cave_System_Complex (复杂洞穴系统)
• Feature_Sinkhole_Natural (天然塌陷坑)
• Feature_Sinkhole_Artificial (人工塌陷坑)
• Feature_Crater_Impact (撞击坑)
• Feature_Crater_Volcanic (火山口)
• Feature_Crater_Explosion (爆炸坑)
• Feature_Arch_Natural_Stone (天然石拱)
• Feature_Arch_Natural_Ice (天然冰拱)
• Feature_Pillar_Rock_Weathered (风化岩柱)
• Feature_Pillar_Rock_Volcanic (火山岩柱)
• Feature_Spire_Rock_Tall (高耸岩尖)
• Feature_Outcrop_Rock_Exposed (裸露岩石露头)
• Feature_Ledge_Rock_Narrow (狭窄岩石平台)
• Feature_Overhang_Rock_Shelter (岩石悬挑庇护所)
• Feature_Crevice_Rock_Deep (深岩石裂缝)
• Feature_Fault_Geological_Visible (可见地质断层)
• Feature_Scree_Slope_Loose (松散碎石坡)
• Feature_Talus_Cone_Debris (岩屑锥)

2.1.3 末日特色地形 (Post-Apocalyptic Terrain):
• Feature_Crater_Nuclear_Large (大型核弹坑)
• Feature_Crater_Nuclear_Small (小型核弹坑)
• Feature_Crater_Weapon_Experimental (实验武器弹坑)
• Feature_Trench_Military_Defensive (军事防御壕沟)
• Feature_Trench_AntiTank_Barrier (反坦克壕沟)
• Feature_Bunker_Underground_Entrance (地下掩体入口)
• Feature_Tunnel_Subway_Collapsed (坍塌地铁隧道)
• Feature_Tunnel_Utility_Damaged (损坏公用隧道)
• Feature_Quarry_Abandoned_Flooded (废弃采石场 - 积水)
• Feature_Quarry_Abandoned_Dry (废弃采石场 - 干涸)
• Feature_Mine_Entrance_Sealed (封闭矿井入口)
• Feature_Mine_Entrance_Open (开放矿井入口)
• Feature_Landfill_Massive_Overgrown (巨型垃圾填埋场 - 植被覆盖)
• Feature_Landfill_Active_Smoking (活跃垃圾场 - 冒烟)
• Feature_Spillway_Dam_Broken (破损大坝泄洪道)
• Feature_Reservoir_Drained_Empty (排空水库)
• Feature_Canal_Artificial_Dry (人工运河 - 干涸)
• Feature_Levee_Flood_Breached (决堤防洪堤)

2.1.4 极端环境地形 (Extreme Environment Terrain):
• Feature_Glacier_Crevasse_Deep (深冰川裂缝)
• Feature_Glacier_Terminus_Melting (融化冰川末端)
• Feature_Iceberg_Stranded_Large (搁浅大冰山)
• Feature_Permafrost_Mound_Thawed (融化永冻土丘)
• Feature_Dune_Sand_Shifting (移动沙丘)
• Feature_Dune_Sand_Stabilized (稳定沙丘)
• Feature_Oasis_Desert_Small (小型沙漠绿洲)
• Feature_Oasis_Desert_Large (大型沙漠绿洲)
• Feature_SaltFlat_Crystalline (结晶盐滩)
• Feature_SaltFlat_Muddy (泥泞盐滩)
• Feature_Geyser_Hot_Active (活跃热间歇泉)
• Feature_Geyser_Hot_Dormant (休眠热间歇泉)
• Feature_HotSpring_Natural_Clear (天然清澈温泉)
• Feature_HotSpring_Natural_Sulfurous (天然硫磺温泉)
• Feature_Fumarole_Volcanic_Steaming (冒蒸汽火山喷气孔)
• Feature_LavaFlow_Cooled_Recent (近期冷却熔岩流)
• Feature_LavaFlow_Cooled_Ancient (古老冷却熔岩流)
• Feature_LavaTube_Entrance_Visible (可见熔岩管入口)
• Feature_Caldera_Volcanic_Large (大型火山口)
• Feature_Caldera_Volcanic_Flooded (积水火山口)

2.2 植被系统 (Vegetation System)

2.2.1 树木类型 (Tree Types):
• Tree_Oak_Mature (成熟橡树)
• Tree_Oak_Young (幼年橡树)
• Tree_Oak_Ancient (古老橡树)
• Tree_Pine_Tall (高大松树)
• Tree_Pine_Dwarf (矮松)
• Tree_Pine_Cluster (松树群)
• Tree_Birch_White (白桦树)
• Tree_Birch_Silver (银桦树)
• Tree_Maple_Red (红枫)
• Tree_Maple_Sugar (糖枫)
• Tree_Willow_Weeping (垂柳)
• Tree_Willow_Upright (直立柳)
• Tree_Elm_American (美国榆树)
• Tree_Elm_Diseased (病害榆树)
• Tree_Ash_Mountain (山梣树)
• Tree_Ash_White (白梣树)
• Tree_Poplar_Tall (高杨树)
• Tree_Poplar_Lombardy (伦巴第杨)
• Tree_Cedar_Red (红雪松)
• Tree_Cedar_White (白雪松)
• Tree_Fir_Balsam (香脂冷杉)
• Tree_Fir_Douglas (道格拉斯冷杉)
• Tree_Spruce_Blue (蓝云杉)
• Tree_Spruce_Norway (挪威云杉)
• Tree_Hemlock_Eastern (东部铁杉)
• Tree_Hemlock_Western (西部铁杉)

2.2.2 末日变异树木 (Post-Apocalyptic Mutated Trees):
• Tree_Dead_Leafless (枯死无叶树)
• Tree_Dead_Hollow (空心枯树)
• Tree_Burnt_Charred (烧焦树木)
• Tree_Burnt_Skeleton (烧毁骨架树)
• Tree_Mutant_Twisted (变异扭曲树)
• Tree_Mutant_Oversized (变异巨大树)
• Tree_Mutant_MultiTrunk (变异多主干树)
• Tree_Diseased_Canker (溃疡病树)
• Tree_Diseased_Blight (枯萎病树)
• Tree_Radioactive_Glowing (辐射发光树)
• Tree_Crystallized_Partial (部分结晶树)
• Tree_Crystallized_Complete (完全结晶树)
• Tree_Fungal_Infected (真菌感染树)
• Tree_Fungal_Overgrown (真菌过度生长树)
• Tree_Metal_Hybrid (金属混合树)
• Tree_Synthetic_Artificial (人工合成树)

2.2.3 灌木和小型植被 (Shrubs and Small Vegetation):
• Shrub_Berry_Edible (可食用浆果灌木)
• Shrub_Berry_Poisonous (有毒浆果灌木)
• Shrub_Thorn_Dense (密集荆棘灌木)
• Shrub_Thorn_Sparse (稀疏荆棘灌木)
• Shrub_Rose_Wild (野玫瑰灌木)
• Shrub_Rose_Cultivated (栽培玫瑰灌木)
• Shrub_Juniper_Low (低矮杜松)
• Shrub_Juniper_Tall (高大杜松)
• Shrub_Lilac_Purple (紫丁香灌木)
• Shrub_Lilac_White (白丁香灌木)
• Shrub_Azalea_Flowering (开花杜鹃)
• Shrub_Azalea_Dormant (休眠杜鹃)
• Shrub_Rhododendron_Large (大型杜鹃花)
• Shrub_Rhododendron_Dwarf (矮杜鹃花)
• Shrub_Forsythia_Yellow (黄连翘)
• Shrub_Forsythia_Bare (光秃连翘)
• Shrub_Boxwood_Trimmed (修剪黄杨)
• Shrub_Boxwood_Wild (野生黄杨)
• Shrub_Privet_Hedge (女贞树篱)
• Shrub_Privet_Overgrown (过度生长女贞)

2.2.4 地面覆盖植物 (Ground Cover Plants):
• Grass_Short_Manicured (修剪短草)
• Grass_Short_Wild (野生短草)
• Grass_Tall_Prairie (草原高草)
• Grass_Tall_Marsh (沼泽高草)
• Grass_Dead_Brown (枯死褐草)
• Grass_Dead_Yellow (枯死黄草)
• Fern_Bracken_Large (大型蕨菜)
• Fern_Bracken_Small (小型蕨菜)
• Fern_Royal_Tall (高大皇家蕨)
• Fern_Maidenhair_Delicate (精细铁线蕨)
• Moss_Green_Thick (厚绿苔藓)
• Moss_Green_Thin (薄绿苔藓)
• Moss_Brown_Dry (干燥褐苔藓)
• Moss_Luminescent_Blue (发光蓝苔藓)
• Lichen_Gray_Crusty (灰色硬壳地衣)
• Lichen_Orange_Bright (鲜橙地衣)
• Lichen_Green_Leafy (绿色叶状地衣)
• Ivy_English_Climbing (攀爬英国常春藤)
• Ivy_Poison_Dangerous (危险毒藤)
• Ivy_Dead_Withered (枯萎常春藤)

2.2.5 农作物和栽培植物 (Crops and Cultivated Plants):
• Crop_Corn_Healthy (健康玉米)
• Crop_Corn_Withered (枯萎玉米)
• Crop_Corn_Harvested (收获后玉米茬)
• Crop_Wheat_Golden (金黄小麦)
• Crop_Wheat_Green (绿色小麦)
• Crop_Wheat_Rotting (腐烂小麦)
• Crop_Soybean_Mature (成熟大豆)
• Crop_Soybean_Young (幼嫩大豆)
• Crop_Potato_Flowering (开花土豆)
• Crop_Potato_Harvested (收获土豆田)
• Crop_Tomato_Ripe (成熟番茄)
• Crop_Tomato_Green (绿色番茄)
• Crop_Sunflower_Blooming (盛开向日葵)
• Crop_Sunflower_Dead (枯死向日葵)
• Crop_Cotton_White (白色棉花)
• Crop_Cotton_Brown (褐色棉花)
• Garden_Vegetable_Organized (有序菜园)
• Garden_Vegetable_Overgrown (过度生长菜园)
• Garden_Flower_Colorful (彩色花园)
• Garden_Flower_Wilted (枯萎花园)

2.2.6 水生植物 (Aquatic Plants):
• Cattail_Tall_Dense (高密香蒲)
• Cattail_Tall_Sparse (高稀香蒲)
• Cattail_Short_Cluster (短簇香蒲)
• Reed_Common_Thick (厚普通芦苇)
• Reed_Common_Thin (薄普通芦苇)
• Reed_Giant_Towering (高耸巨芦苇)
• Lily_Water_White (白睡莲)
• Lily_Water_Pink (粉睡莲)
• Lily_Water_Yellow (黄睡莲)
• Lotus_Sacred_Large (大型圣莲)
• Lotus_Sacred_Small (小型圣莲)
• Algae_Green_Surface (绿色表面藻类)
• Algae_Blue_Toxic (蓝色有毒藻类)
• Algae_Red_Bloom (红色藻华)
• Kelp_Brown_Long (长褐海带)
• Kelp_Brown_Short (短褐海带)
• Seaweed_Green_Floating (漂浮绿海草)
• Seaweed_Brown_Anchored (固定褐海草)

2.3 岩石和矿物系统 (Rock and Mineral System)

2.3.1 基础岩石类型 (Basic Rock Types):
• Rock_Boulder_Granite_Large (大型花岗岩巨石)
• Rock_Boulder_Granite_Medium (中型花岗岩巨石)
• Rock_Boulder_Granite_Small (小型花岗岩巨石)
• Rock_Boulder_Limestone_Large (大型石灰岩巨石)
• Rock_Boulder_Limestone_Medium (中型石灰岩巨石)
• Rock_Boulder_Limestone_Small (小型石灰岩巨石)
• Rock_Boulder_Sandstone_Large (大型砂岩巨石)
• Rock_Boulder_Sandstone_Medium (中型砂岩巨石)
• Rock_Boulder_Sandstone_Small (小型砂岩巨石)
• Rock_Boulder_Basalt_Large (大型玄武岩巨石)
• Rock_Boulder_Basalt_Medium (中型玄武岩巨石)
• Rock_Boulder_Basalt_Small (小型玄武岩巨石)
• Rock_Outcrop_Granite_Weathered (风化花岗岩露头)
• Rock_Outcrop_Limestone_Karst (喀斯特石灰岩露头)
• Rock_Outcrop_Sandstone_Layered (层状砂岩露头)
• Rock_Outcrop_Shale_Fractured (破碎页岩露头)
• Rock_Outcrop_Slate_Smooth (光滑板岩露头)
• Rock_Outcrop_Quartzite_Crystalline (结晶石英岩露头)

2.3.2 散石和碎石 (Scattered Rocks and Debris):
• Rock_Scatter_Pebbles_Small (小鹅卵石散布)
• Rock_Scatter_Pebbles_Large (大鹅卵石散布)
• Rock_Scatter_Cobbles_Round (圆形卵石散布)
• Rock_Scatter_Cobbles_Angular (棱角卵石散布)
• Rock_Gravel_Fine_Gray (细灰色砾石)
• Rock_Gravel_Fine_Brown (细褐色砾石)
• Rock_Gravel_Coarse_Mixed (粗混合砾石)
• Rock_Gravel_Coarse_Uniform (粗均匀砾石)
• Rock_Rubble_Construction_Concrete (建筑混凝土瓦砾)
• Rock_Rubble_Construction_Brick (建筑砖块瓦砾)
• Rock_Rubble_Natural_Limestone (天然石灰岩瓦砾)
• Rock_Rubble_Natural_Granite (天然花岗岩瓦砾)
• Rock_Talus_Slope_Fresh (新鲜岩屑坡)
• Rock_Talus_Slope_Weathered (风化岩屑坡)
• Rock_Scree_Loose_Unstable (松散不稳定碎石)
• Rock_Scree_Compact_Stable (紧实稳定碎石)

2.3.3 特殊岩石形态 (Special Rock Formations):
• Rock_Formation_Arch_Natural (天然岩石拱门)
• Rock_Formation_Bridge_Natural (天然岩石桥)
• Rock_Formation_Pillar_Erosion (侵蚀岩柱)
• Rock_Formation_Spire_Volcanic (火山岩尖)
• Rock_Formation_Mesa_Flat (平顶岩台)
• Rock_Formation_Butte_Isolated (孤立岩丘)
• Rock_Formation_Hoodoo_Tall (高岩柱群)
• Rock_Formation_Balanced_Precarious (危险平衡岩)
• Rock_Formation_Stack_Sea (海蚀柱)
• Rock_Formation_Cave_Entrance (岩洞入口)
• Rock_Formation_Overhang_Shelter (岩石悬挑庇护所)
• Rock_Formation_Crevice_Deep (深岩石裂缝)
• Rock_Formation_Slot_Canyon (狭缝峡谷)
• Rock_Formation_Amphitheater_Natural (天然圆形剧场)

2.3.4 矿物和晶体 (Minerals and Crystals):
• Crystal_Quartz_Clear_Large (大型透明石英晶体)
• Crystal_Quartz_Clear_Small (小型透明石英晶体)
• Crystal_Quartz_Smoky_Cluster (烟水晶簇)
• Crystal_Quartz_Rose_Pink (粉色玫瑰石英)
• Crystal_Amethyst_Purple_Geode (紫水晶晶洞)
• Crystal_Amethyst_Purple_Point (紫水晶尖)
• Crystal_Calcite_White_Rhomb (白色方解石菱面体)
• Crystal_Calcite_Orange_Cluster (橙色方解石簇)
• Crystal_Fluorite_Green_Cubic (绿色萤石立方体)
• Crystal_Fluorite_Purple_Octahedral (紫色萤石八面体)
• Crystal_Pyrite_Gold_Cubic (金色黄铁矿立方体)
• Crystal_Pyrite_Gold_Cluster (金色黄铁矿簇)
• Crystal_Hematite_Metallic_Botryoidal (金属光泽肾状赤铁矿)
• Crystal_Magnetite_Black_Octahedral (黑色磁铁矿八面体)
• Crystal_Malachite_Green_Banded (绿色带状孔雀石)
• Crystal_Azurite_Blue_Crystalline (蓝色结晶蓝铜矿)

2.4 水体系统 (Water Body System)

2.4.1 河流和溪流 (Rivers and Streams):
• Water_River_Large_FastFlow (大型急流河)
• Water_River_Large_SlowFlow (大型缓流河)
• Water_River_Medium_Meandering (中型蜿蜒河)
• Water_River_Medium_Straight (中型直流河)
• Water_River_Small_Mountain (小型山溪)
• Water_River_Small_Prairie (小型草原溪)
• Water_Stream_Clear_Shallow (清澈浅溪)
• Water_Stream_Muddy_Deep (浑浊深溪)
• Water_Creek_Seasonal_Dry (季节性干涸小溪)
• Water_Creek_Seasonal_Flowing (季节性流水小溪)
• Water_Brook_Babbling_Rocky (潺潺岩石小溪)
• Water_Brook_Quiet_Sandy (安静沙底小溪)
• Water_Rapids_Whitewater_Dangerous (危险白水急流)
• Water_Rapids_Gentle_Navigable (温和可航行急流)
• Water_Waterfall_High_Thundering (高耸雷鸣瀑布)
• Water_Waterfall_Medium_Cascading (中型阶梯瀑布)
• Water_Waterfall_Low_Gentle (低矮温和瀑布)
• Water_Waterfall_Frozen_Winter (冬季冰冻瀑布)

2.4.2 湖泊和池塘 (Lakes and Ponds):
• Water_Lake_Large_Deep (大型深湖)
• Water_Lake_Large_Shallow (大型浅湖)
• Water_Lake_Medium_Clear (中型清澈湖)
• Water_Lake_Medium_Murky (中型浑浊湖)
• Water_Lake_Small_Mountain (小型山湖)
• Water_Lake_Small_Prairie (小型草原湖)
• Water_Pond_Farm_Livestock (农场牲畜池塘)
• Water_Pond_Garden_Ornamental (花园观赏池塘)
• Water_Pond_Natural_Wildlife (天然野生动物池塘)
• Water_Pond_Artificial_Retention (人工蓄水池)
• Water_Pool_Rock_Natural (天然岩石池)
• Water_Pool_Tide_Coastal (海岸潮汐池)
• Water_Pool_Hot_Geothermal (地热温泉池)
• Water_Pool_Mud_Volcanic (火山泥浆池)
• Water_Reservoir_Dam_Large (大型水坝水库)
• Water_Reservoir_Dam_Small (小型水坝水库)
• Water_Reservoir_Natural_Glacial (天然冰川水库)
• Water_Reservoir_Artificial_Irrigation (人工灌溉水库)

2.4.3 海洋和海岸 (Oceans and Coastlines):
• Water_Ocean_Deep_Blue (深蓝海洋)
• Water_Ocean_Shallow_Turquoise (浅绿松石海洋)
• Water_Sea_Calm_Peaceful (平静安详海面)
• Water_Sea_Rough_Stormy (汹涌暴风雨海面)
• Water_Bay_Protected_Sheltered (受保护的避风海湾)
• Water_Bay_Open_Exposed (开放暴露海湾)
• Water_Cove_Small_Hidden (小型隐蔽海湾)
• Water_Inlet_Narrow_Deep (狭窄深入海湾)
• Water_Strait_Narrow_Current (狭窄海峡急流)
• Water_Channel_Deep_Navigable (深水可航行海峡)
• Water_Lagoon_Shallow_Tropical (热带浅水泻湖)
• Water_Lagoon_Brackish_Coastal (海岸咸淡水泻湖)
• Water_Estuary_River_Mouth (河口三角洲)
• Water_Delta_Sediment_Rich (富含沉积物三角洲)
• Water_Fjord_Deep_Glacial (深冰川峡湾)
• Water_Sound_Wide_Shallow (宽阔浅水海峡)

2.4.4 末日污染水体 (Post-Apocalyptic Contaminated Water):
• Water_Polluted_Chemical_Green (化学污染绿水)
• Water_Polluted_Chemical_Orange (化学污染橙水)
• Water_Polluted_Oil_Black (石油污染黑水)
• Water_Polluted_Radioactive_Glowing (辐射污染发光水)
• Water_Toxic_Foam_Surface (表面有毒泡沫水)
• Water_Acidic_Corroded_Banks (酸性腐蚀河岸水)
• Water_Sewage_Raw_Stagnant (原始污水停滞)
• Water_Industrial_Waste_Colored (工业废水有色)
• Water_Algae_Bloom_Toxic (有毒藻华水)
• Water_Dead_Fish_Floating (漂浮死鱼水)
• Water_Debris_Plastic_Floating (漂浮塑料垃圾水)
• Water_Scum_Bacterial_Surface (表面细菌浮渣水)

========================================
3.0 建筑类 (Building Elements)
========================================

此分类涵盖构成建筑物主体和重要组成部分的模块，为末日世界提供人造结构背景。

3.1 建筑类型分类 (Building Type Classification)

3.1.1 住宅建筑 (Residential Buildings):
• Building_House_SingleFamily_Small (小型独栋住宅)
• Building_House_SingleFamily_Large (大型独栋住宅)
• Building_House_TwoStory_Traditional (传统两层住宅)
• Building_House_Ranch_SingleStory (单层牧场式住宅)
• Building_House_Colonial_Historic (历史殖民地式住宅)
• Building_House_Victorian_Ornate (华丽维多利亚式住宅)
• Building_House_Modern_Contemporary (现代当代住宅)
• Building_House_Cabin_Log (原木小屋)
• Building_House_Cottage_Small (小型村舍)
• Building_House_Mansion_Estate (庄园豪宅)
• Building_Apartment_LowRise_2to4Story (低层公寓楼2-4层)
• Building_Apartment_MidRise_5to8Story (中层公寓楼5-8层)
• Building_Apartment_HighRise_9PlusStory (高层公寓楼9层以上)
• Building_Townhouse_Row_Connected (联排别墅)
• Building_Duplex_TwoFamily (双拼住宅)
• Building_Condominium_Luxury (豪华公寓)
• Building_Trailer_Mobile_Single (单体活动房屋)
• Building_Trailer_Mobile_Double (双体活动房屋)
• Building_Dormitory_Student (学生宿舍)
• Building_Barracks_Military (军营宿舍)

3.1.2 商业建筑 (Commercial Buildings):
• Building_Store_Convenience_Small (小型便利店)
• Building_Store_Grocery_Medium (中型杂货店)
• Building_Store_Department_Large (大型百货商店)
• Building_Supermarket_Chain (连锁超市)
• Building_Mall_Shopping_Enclosed (封闭式购物中心)
• Building_Mall_Shopping_Strip (条形购物中心)
• Building_Restaurant_FastFood (快餐店)
• Building_Restaurant_Casual_Dining (休闲餐厅)
• Building_Restaurant_Fine_Dining (高档餐厅)
• Building_Cafe_Coffee_Small (小型咖啡馆)
• Building_Bar_Tavern_Local (当地酒吧)
• Building_Nightclub_Entertainment (娱乐夜总会)
• Building_Hotel_Budget (经济型酒店)
• Building_Hotel_Luxury (豪华酒店)
• Building_Motel_Roadside (路边汽车旅馆)
• Building_Office_Small_Professional (小型专业办公楼)
• Building_Office_Medium_Corporate (中型企业办公楼)
• Building_Office_Large_Skyscraper (大型摩天办公楼)
• Building_Bank_Local_Branch (当地银行分行)
• Building_Bank_Regional_Headquarters (地区银行总部)
• Building_Pharmacy_Chain (连锁药店)

3.1.3 工业建筑 (Industrial Buildings):
• Building_Factory_Light_Manufacturing (轻工制造厂)
• Building_Factory_Heavy_Manufacturing (重工制造厂)
• Building_Factory_Automotive_Assembly (汽车装配厂)
• Building_Factory_Chemical_Processing (化工处理厂)
• Building_Factory_Food_Processing (食品加工厂)
• Building_Factory_Textile_Production (纺织生产厂)
• Building_Warehouse_Storage_Small (小型仓储库)
• Building_Warehouse_Storage_Large (大型仓储库)
• Building_Warehouse_Distribution_Center (配送中心)
• Building_Warehouse_Cold_Storage (冷藏仓库)
• Building_Plant_Power_Coal (燃煤发电厂)
• Building_Plant_Power_Nuclear (核电厂)
• Building_Plant_Power_Hydroelectric (水力发电厂)
• Building_Plant_Power_Solar (太阳能发电厂)
• Building_Plant_Power_Wind (风力发电厂)
• Building_Plant_Water_Treatment (水处理厂)
• Building_Plant_Sewage_Treatment (污水处理厂)
• Building_Plant_Waste_Incineration (垃圾焚烧厂)
• Building_Refinery_Oil_Petroleum (石油炼制厂)
• Building_Refinery_Chemical_Complex (化工综合体)

3.1.4 公共建筑 (Public Buildings):
• Building_School_Elementary (小学)
• Building_School_Middle (中学)
• Building_School_High (高中)
• Building_University_Campus_Main (大学主校区)
• Building_University_Dormitory (大学宿舍)
• Building_University_Library (大学图书馆)
• Building_Hospital_General_Small (小型综合医院)
• Building_Hospital_General_Large (大型综合医院)
• Building_Hospital_Specialty_Clinic (专科诊所)
• Building_Hospital_Emergency_Center (急救中心)
• Building_Library_Public_Branch (公共图书馆分馆)
• Building_Library_Public_Main (公共图书馆总馆)
• Building_Museum_Art (艺术博物馆)
• Building_Museum_History (历史博物馆)
• Building_Museum_Science (科学博物馆)
• Building_Theater_Performance (表演剧院)
• Building_Theater_Movie_Cinema (电影院)
• Building_Stadium_Sports_Large (大型体育场)
• Building_Arena_Indoor_Sports (室内体育馆)
• Building_Pool_Swimming_Public (公共游泳池)
• Building_Gym_Fitness_Center (健身中心)

3.1.5 政府和服务建筑 (Government and Service Buildings):
• Building_CityHall_Municipal (市政厅)
• Building_Courthouse_County (县法院)
• Building_Police_Station_Local (当地警察局)
• Building_Police_Station_State (州警察局)
• Building_Fire_Station_Volunteer (志愿消防站)
• Building_Fire_Station_Professional (专业消防站)
• Building_PostOffice_Local (当地邮局)
• Building_PostOffice_Regional (地区邮局)
• Building_DMV_Motor_Vehicle (机动车管理局)
• Building_Social_Services_Office (社会服务办公室)
• Building_Unemployment_Office (失业办公室)
• Building_Tax_Assessor_Office (税务评估办公室)
• Building_Utility_Electric_Substation (电力变电站)
• Building_Utility_Water_Pumping (水泵站)
• Building_Utility_Gas_Distribution (燃气配送站)
• Building_Utility_Telecommunications (电信设施)
• Building_Airport_Terminal_Small (小型机场航站楼)
• Building_Airport_Terminal_Large (大型机场航站楼)
• Building_Airport_Hangar_Private (私人飞机库)
• Building_Airport_Hangar_Commercial (商用飞机库)

3.1.6 宗教建筑 (Religious Buildings):
• Building_Church_Protestant_Small (小型新教教堂)
• Building_Church_Protestant_Large (大型新教教堂)
• Building_Church_Catholic_Parish (天主教堂区教堂)
• Building_Church_Catholic_Cathedral (天主教大教堂)
• Building_Synagogue_Orthodox (正统犹太会堂)
• Building_Synagogue_Reform (改革犹太会堂)
• Building_Mosque_Community (社区清真寺)
• Building_Mosque_Grand (大清真寺)
• Building_Temple_Buddhist (佛教寺庙)
• Building_Temple_Hindu (印度教神庙)
• Building_Chapel_Wedding (婚礼小教堂)
• Building_Chapel_Funeral (殡仪小教堂)
• Building_Monastery_Secluded (隐修修道院)
• Building_Convent_Nuns (修女院)
• Building_Seminary_Religious (宗教神学院)
• Building_Retreat_Spiritual (精神静修所)

3.2 建筑状态和损坏程度 (Building Condition and Damage Level)

3.2.1 整体状态 (Overall Condition):
• State_Building_Pristine_New (崭新完美状态)
• State_Building_Excellent_WellMaintained (优秀维护状态)
• State_Building_Good_MinorWear (良好轻微磨损)
• State_Building_Fair_ModerateWear (一般中等磨损)
• State_Building_Poor_SignificantDamage (较差显著损坏)
• State_Building_Dilapidated_SevereDamage (破旧严重损坏)
• State_Building_Ruined_StructuralFailure (废墟结构失效)
• State_Building_Collapsed_PartialDestruction (坍塌部分摧毁)
• State_Building_Demolished_CompleteDestruction (拆除完全摧毁)

3.2.2 具体损坏类型 (Specific Damage Types):
• Damage_Fire_Burnt_Exterior (外部火烧损坏)
• Damage_Fire_Burnt_Interior (内部火烧损坏)
• Damage_Fire_Smoke_Stained (烟熏污渍)
• Damage_Water_Flood_Basement (地下室洪水损坏)
• Damage_Water_Leak_Roof (屋顶漏水损坏)
• Damage_Water_Pipe_Burst (管道爆裂损坏)
• Damage_Storm_Wind_Roof (风暴屋顶损坏)
• Damage_Storm_Hail_Siding (冰雹外墙损坏)
• Damage_Storm_Lightning_Strike (雷击损坏)
• Damage_Earthquake_Structural_Cracks (地震结构裂缝)
• Damage_Earthquake_Foundation_Shift (地震地基移位)
• Damage_Explosion_Blast_Damage (爆炸冲击损坏)
• Damage_Vehicle_Impact_Collision (车辆撞击损坏)
• Damage_Vandalism_Graffiti (破坏涂鸦)
• Damage_Vandalism_Broken_Windows (破坏破窗)
• Damage_Age_Weathering_Natural (自然老化风化)
• Damage_Age_Structural_Settling (结构沉降)
• Damage_Pest_Termite_Wood (白蚁木材损坏)
• Damage_Pest_Rodent_Infestation (啮齿动物侵扰)
• Damage_Mold_Moisture_Growth (潮湿霉菌生长)

3.2.3 末日特色损坏 (Post-Apocalyptic Damage):
• Damage_Nuclear_Radiation_Exposure (核辐射暴露损坏)
• Damage_Nuclear_Blast_Thermal (核爆热辐射损坏)
• Damage_Chemical_Corrosion_Acid (化学酸腐蚀损坏)
• Damage_Biological_Contamination (生物污染损坏)
• Damage_Zombie_Infestation_Breach (丧尸侵扰破坏)
• Damage_Military_Combat_Bullet (军事战斗子弹损坏)
• Damage_Military_Combat_Shrapnel (军事战斗弹片损坏)
• Damage_Looting_Ransacked_Interior (抢劫洗劫内部)
• Damage_Abandonment_Neglect_Decay (遗弃忽视腐朽)
• Damage_Overgrowth_Vegetation_Invasion (植被过度生长侵入)

3.3 建筑材料和外观 (Building Materials and Appearance)

3.3.1 主要建筑材料 (Primary Building Materials):
• Material_Brick_Red_Traditional (传统红砖)
• Material_Brick_Yellow_Sandstone (黄砂岩砖)
• Material_Brick_White_Painted (白色涂漆砖)
• Material_Stone_Granite_Natural (天然花岗岩石)
• Material_Stone_Limestone_Cut (切割石灰岩)
• Material_Stone_Fieldstone_Rough (粗糙田石)
• Material_Concrete_Poured_Smooth (浇筑光滑混凝土)
• Material_Concrete_Block_Standard (标准混凝土砌块)
• Material_Concrete_Precast_Panel (预制混凝土板)
• Material_Wood_Frame_Traditional (传统木框架)
• Material_Wood_Log_Natural (天然原木)
• Material_Wood_Siding_Horizontal (水平木壁板)
• Material_Wood_Shingle_Cedar (雪松木瓦)
• Material_Metal_Steel_Frame (钢框架)
• Material_Metal_Aluminum_Siding (铝制壁板)
• Material_Metal_Corrugated_Sheet (波纹金属板)
• Material_Glass_Curtain_Wall (玻璃幕墙)
• Material_Glass_Block_Decorative (装饰玻璃砖)
• Material_Stucco_Textured_Finish (纹理灰泥饰面)
• Material_Vinyl_Siding_Maintenance_Free (免维护乙烯基壁板)

3.3.2 屋顶材料和类型 (Roof Materials and Types):
• Roof_Asphalt_Shingle_Standard (标准沥青瓦)
• Roof_Asphalt_Shingle_Architectural (建筑沥青瓦)
• Roof_Metal_Standing_Seam (立缝金属屋顶)
• Roof_Metal_Corrugated_Galvanized (镀锌波纹金属)
• Roof_Tile_Clay_Red (红色粘土瓦)
• Roof_Tile_Concrete_Gray (灰色混凝土瓦)
• Roof_Slate_Natural_Blue (天然蓝色板岩)
• Roof_Wood_Shake_Cedar (雪松木摇摆瓦)
• Roof_Rubber_Membrane_Flat (平屋顶橡胶膜)
• Roof_Tar_Gravel_Built_Up (沥青砾石建筑屋顶)
• Roof_Green_Living_Vegetated (绿色植被屋顶)
• Roof_Solar_Panel_Integrated (集成太阳能板屋顶)
• Roof_Thatch_Straw_Traditional (传统茅草屋顶)
• Roof_Sod_Earth_Scandinavian (斯堪的纳维亚草皮屋顶)

3.3.3 屋顶形状 (Roof Shapes):
• RoofShape_Gable_Simple (简单人字形)
• RoofShape_Hip_Four_Sided (四坡屋顶)
• RoofShape_Shed_Single_Slope (单坡屋顶)
• RoofShape_Flat_Modern (现代平屋顶)
• RoofShape_Mansard_French (法式复折屋顶)
• RoofShape_Gambrel_Barn_Style (谷仓式复折屋顶)
• RoofShape_Dome_Circular (圆形穹顶)
• RoofShape_Pyramid_Square (方形金字塔屋顶)
• RoofShape_Butterfly_V_Shaped (蝴蝶V形屋顶)
• RoofShape_Sawtooth_Industrial (锯齿形工业屋顶)
• RoofShape_Monitor_Raised_Center (凸起中央监控屋顶)
• RoofShape_Clerestory_Windowed (高侧窗屋顶)

3.4 建筑风格和时代 (Architectural Styles and Periods)

3.4.1 历史建筑风格 (Historical Architectural Styles):
• Style_Colonial_American_Early (早期美国殖民地风格)
• Style_Georgian_Symmetrical_Formal (对称正式乔治亚风格)
• Style_Federal_Neoclassical_Refined (精致新古典联邦风格)
• Style_Greek_Revival_Temple_Like (神庙式希腊复兴风格)
• Style_Gothic_Revival_Pointed_Arches (尖拱哥特复兴风格)
• Style_Italianate_Renaissance_Inspired (文艺复兴意大利风格)
• Style_Second_Empire_Mansard_Roof (第二帝国复折屋顶风格)
• Style_Queen_Anne_Victorian_Ornate (华丽安妮女王维多利亚风格)
• Style_Shingle_Style_Coastal (海岸木瓦风格)
• Style_Romanesque_Revival_Massive (厚重罗马复兴风格)
• Style_Beaux_Arts_Classical_Grand (宏伟古典美术学院风格)
• Style_Prairie_School_Horizontal (水平草原学派风格)

3.4.2 现代建筑风格 (Modern Architectural Styles):
• Style_Art_Deco_Geometric_Ornament (几何装饰艺术风格)
• Style_International_Glass_Steel (国际玻璃钢风格)
• Style_Mid_Century_Modern_Clean (简洁中世纪现代风格)
• Style_Brutalist_Concrete_Massive (厚重野兽派混凝土风格)
• Style_Postmodern_Eclectic_Playful (折衷游戏后现代风格)
• Style_High_Tech_Industrial_Exposed (暴露工业高科技风格)
• Style_Deconstructivist_Fragmented (碎片解构主义风格)
• Style_Green_Sustainable_Eco (生态可持续绿色风格)
• Style_Minimalist_Simple_Clean (简洁极简主义风格)
• Style_Contemporary_Mixed_Current (当代混合风格)

3.4.3 地域建筑风格 (Regional Architectural Styles):
• Style_Adobe_Southwestern_Desert (西南沙漠土坯风格)
• Style_Log_Cabin_Frontier_Rustic (边疆乡村原木小屋风格)
• Style_Plantation_Southern_Columned (南方种植园柱式风格)
• Style_Cape_Cod_New_England (新英格兰科德角风格)
• Style_Ranch_Western_Sprawling (西部蔓延牧场风格)
• Style_Craftsman_Bungalow_Handmade (手工艺平房风格)
• Style_Spanish_Colonial_Mission (西班牙殖民传教风格)
• Style_French_Colonial_Creole (法国殖民克里奥尔风格)
• Style_German_Colonial_Stone (德国殖民石材风格)
• Style_Dutch_Colonial_Gambrel (荷兰殖民复折风格)

========================================
4.0 装饰类 (Decorative Elements/Props)
========================================

此分类主要指独立于建筑主体结构，用于丰富场景、提供掩护或叙事的室外道具和装饰元素。

4.1 街道家具和城市设施 (Street Furniture and Urban Infrastructure)

4.1.1 座椅和休息设施 (Seating and Rest Facilities):
• Furniture_Bench_Park_Wood_Traditional (传统木制公园长椅)
• Furniture_Bench_Park_Metal_Modern (现代金属公园长椅)
• Furniture_Bench_Bus_Stop_Shelter (公交站候车椅)
• Furniture_Bench_Memorial_Stone (纪念石椅)
• Furniture_Picnic_Table_Wood_Standard (标准木制野餐桌)
• Furniture_Picnic_Table_Concrete_Permanent (永久混凝土野餐桌)
• Furniture_Chair_Outdoor_Single (单人户外椅)
• Furniture_Gazebo_Park_Octagonal (八角公园凉亭)
• Furniture_Pavilion_Shelter_Large (大型遮蔽亭)
• Furniture_Pergola_Garden_Climbing (花园攀爬棚架)

4.1.2 照明设施 (Lighting Infrastructure):
• Light_Street_Lamp_Traditional_Cast_Iron (传统铸铁路灯)
• Light_Street_Lamp_Modern_LED (现代LED路灯)
• Light_Park_Lamp_Decorative_Globe (装饰球形公园灯)
• Light_Flood_Light_Security_Bright (明亮安全泛光灯)
• Light_Pathway_Light_Low_Garden (低矮花园小径灯)
• Light_Bollard_Light_Driveway (车道系缆桩灯)
• Light_Solar_Light_Sustainable (可持续太阳能灯)
• Light_Emergency_Light_Battery (电池应急灯)
• Light_Neon_Sign_Commercial (商业霓虹灯招牌)
• Light_Traffic_Light_Intersection (交叉路口交通灯)

4.1.3 垃圾和废物管理 (Waste Management):
• Waste_Trash_Can_Metal_Standard (标准金属垃圾桶)
• Waste_Trash_Can_Plastic_Wheeled (带轮塑料垃圾桶)
• Waste_Recycling_Bin_Sorted (分类回收箱)
• Waste_Dumpster_Large_Commercial (大型商用垃圾箱)
• Waste_Compactor_Apartment_Building (公寓楼垃圾压缩机)
• Waste_Incinerator_Public_Small (小型公共焚烧炉)
• Waste_Compost_Bin_Organic (有机堆肥箱)
• Waste_Hazardous_Container_Sealed (密封危险废物容器)
• Waste_Medical_Disposal_Biohazard (生物危险医疗废物处理)
• Waste_Nuclear_Container_Shielded (屏蔽核废料容器)

4.1.4 通信和信息设施 (Communication and Information):
• Communication_Phone_Booth_Traditional (传统电话亭)
• Communication_Pay_Phone_Wall_Mounted (壁挂付费电话)
• Communication_Cell_Tower_Tall (高大手机信号塔)
• Communication_Antenna_Radio_Large (大型无线电天线)
• Communication_Satellite_Dish_Ground (地面卫星天线)
• Communication_Internet_Kiosk_Public (公共网络亭)
• Communication_Emergency_Call_Box (紧急呼叫箱)
• Communication_Intercom_Building_Entry (建筑入口对讲机)
• Communication_Speaker_Public_Address (公共广播扬声器)
• Communication_Digital_Display_Information (数字信息显示屏)

4.2 标牌和指示系统 (Signage and Wayfinding Systems)

4.2.1 交通标志 (Traffic Signs):
• Sign_Traffic_Stop_Octagonal_Red (八角红色停车标志)
• Sign_Traffic_Yield_Triangular_Yellow (三角黄色让行标志)
• Sign_Traffic_Speed_Limit_Rectangular (矩形限速标志)
• Sign_Traffic_No_Entry_Circular_Red (圆形红色禁止通行)
• Sign_Traffic_One_Way_Directional (单行道方向标志)
• Sign_Traffic_School_Zone_Warning (学校区域警告标志)
• Sign_Traffic_Construction_Orange (橙色施工标志)
• Sign_Traffic_Detour_Temporary (临时绕行标志)
• Sign_Traffic_Parking_Restriction (停车限制标志)
• Sign_Traffic_Pedestrian_Crossing (行人过街标志)

4.2.2 街道和地点标识 (Street and Location Signs):
• Sign_Street_Name_Standard_Green (标准绿色街道名牌)
• Sign_Street_Name_Historic_Brown (历史褐色街道名牌)
• Sign_Highway_Interstate_Blue (蓝色州际公路标志)
• Sign_Highway_State_Route_White (白色州道标志)
• Sign_City_Limit_Welcome (城市界限欢迎标志)
• Sign_Neighborhood_District_Identity (社区区域身份标志)
• Sign_Landmark_Tourist_Attraction (地标旅游景点标志)
• Sign_Historical_Marker_Educational (历史标记教育标志)
• Sign_Mile_Marker_Distance (里程标记距离标志)
• Sign_Exit_Ramp_Highway_Direction (高速公路出口方向标志)

4.2.3 商业和广告标牌 (Commercial and Advertising Signs):
• Sign_Business_Name_Storefront (店面商业名称标志)
• Sign_Restaurant_Menu_Board_Outdoor (户外餐厅菜单板)
• Sign_Gas_Station_Price_Display (加油站价格显示牌)
• Sign_Hotel_Vacancy_Neon (酒店空房霓虹灯)
• Sign_Real_Estate_For_Sale (房地产出售标志)
• Sign_Billboard_Large_Advertising (大型广告牌)
• Sign_Poster_Board_Community (社区海报板)
• Sign_Banner_Event_Temporary (临时活动横幅)
• Sign_Window_Decal_Business_Hours (营业时间窗贴)
• Sign_Awning_Business_Fabric (商业织物遮阳篷)

4.2.4 警告和安全标志 (Warning and Safety Signs):
• Sign_Warning_Danger_High_Voltage (高压危险警告标志)
• Sign_Warning_Radiation_Hazard (辐射危险警告标志)
• Sign_Warning_Biohazard_Contamination (生物危险污染警告)
• Sign_Warning_Chemical_Spill (化学品泄漏警告)
• Sign_Warning_Unstable_Ground (地面不稳定警告)
• Sign_Warning_Falling_Debris (坠落碎片警告)
• Sign_Warning_Toxic_Gas (有毒气体警告)
• Sign_Warning_Quarantine_Zone (隔离区警告)
• Sign_Warning_Military_Restricted (军事限制区警告)
• Sign_Warning_Zombie_Outbreak (丧尸爆发警告)

4.3 围栏和屏障系统 (Fencing and Barrier Systems)

4.3.1 住宅围栏 (Residential Fencing):
• Fence_Wood_Picket_White_Traditional (传统白色尖桩木栅栏)
• Fence_Wood_Privacy_Tall_Solid (高实心隐私木栅栏)
• Fence_Chain_Link_Galvanized_Standard (标准镀锌铁丝网栅栏)
• Fence_Vinyl_Maintenance_Free_White (免维护白色乙烯基栅栏)
• Fence_Wrought_Iron_Decorative_Black (装饰黑色锻铁栅栏)
• Fence_Split_Rail_Rustic_Natural (乡村天然劈栏栅栏)
• Fence_Bamboo_Eco_Friendly_Natural (环保天然竹栅栏)
• Fence_Stone_Wall_Low_Fieldstone (低矮田石围墙)
• Fence_Brick_Wall_Medium_Traditional (中等传统砖围墙)
• Fence_Concrete_Block_High_Modern (高现代混凝土砌块围墙)

4.3.2 商业和工业围栏 (Commercial and Industrial Fencing):
• Fence_Security_Chain_Link_Barbed_Wire (带刺铁丝网安全栅栏)
• Fence_Security_Electric_High_Voltage (高压电安全栅栏)
• Fence_Construction_Orange_Temporary (橙色临时施工栅栏)
• Fence_Crowd_Control_Metal_Portable (便携金属人群控制栅栏)
• Fence_Privacy_Screen_Fabric_Windscreen (织物防风隐私屏)
• Fence_Acoustic_Barrier_Highway_Noise (高速公路噪音隔音屏障)
• Fence_Deer_Wildlife_Exclusion_Tall (高野生动物排斥栅栏)
• Fence_Pool_Safety_Code_Required (法规要求的游泳池安全栅栏)
• Fence_Industrial_Perimeter_Heavy_Duty (重型工业周边栅栏)
• Fence_Military_Razor_Wire_Fortified (军事刀片刺网加固栅栏)

4.3.3 交通屏障 (Traffic Barriers):
• Barrier_Concrete_Jersey_Highway (高速公路混凝土护栏)
• Barrier_Metal_Guardrail_Roadside (路边金属护栏)
• Barrier_Water_Filled_Temporary (临时充水屏障)
• Barrier_Plastic_Construction_Orange (橙色塑料施工屏障)
• Barrier_Tire_Wall_Racing_Safety (赛车安全轮胎墙)
• Barrier_Cable_Median_Flexible (柔性中央分隔带缆索护栏)
• Barrier_Bollard_Steel_Vehicle_Stop (钢制车辆阻挡系缆桩)
• Barrier_Gate_Automatic_Entry_Control (自动入口控制闸门)
• Barrier_Spike_Strip_Security_Retractable (可伸缩安全刺钉带)
• Barrier_Checkpoint_Military_Fortified (军事加固检查点屏障)

4.4 车辆和交通设备 (Vehicles and Transportation Equipment)

4.4.1 民用车辆 (Civilian Vehicles):
• Vehicle_Car_Sedan_Compact (紧凑型轿车)
• Vehicle_Car_Sedan_Midsize (中型轿车)
• Vehicle_Car_Sedan_Luxury (豪华轿车)
• Vehicle_Car_SUV_Small (小型SUV)
• Vehicle_Car_SUV_Large (大型SUV)
• Vehicle_Car_Pickup_Truck_Standard (标准皮卡车)
• Vehicle_Car_Pickup_Truck_Heavy_Duty (重型皮卡车)
• Vehicle_Car_Van_Minivan_Family (家庭小型货车)
• Vehicle_Car_Van_Cargo_Commercial (商用货车)
• Vehicle_Car_Convertible_Sports (敞篷跑车)
• Vehicle_Car_Coupe_Two_Door (双门轿跑车)
• Vehicle_Car_Hatchback_Economy (经济型掀背车)
• Vehicle_Car_Station_Wagon_Family (家庭旅行车)
• Vehicle_Car_Limousine_Stretch (加长豪华轿车)
• Vehicle_Motorcycle_Standard_Street (标准街道摩托车)
• Vehicle_Motorcycle_Cruiser_Touring (巡航旅行摩托车)
• Vehicle_Motorcycle_Sport_Racing (运动赛车摩托车)
• Vehicle_Motorcycle_Dirt_Bike_Off_Road (越野摩托车)
• Vehicle_Bicycle_Road_Racing (公路赛车自行车)
• Vehicle_Bicycle_Mountain_Trail (山地越野自行车)

4.4.2 商用和工业车辆 (Commercial and Industrial Vehicles):
• Vehicle_Truck_Delivery_Small (小型送货卡车)
• Vehicle_Truck_Delivery_Large (大型送货卡车)
• Vehicle_Truck_Semi_Trailer (半挂卡车)
• Vehicle_Truck_Garbage_Waste_Collection (垃圾收集车)
• Vehicle_Truck_Fire_Engine_Rescue (消防救援车)
• Vehicle_Truck_Ambulance_Emergency (急救救护车)
• Vehicle_Truck_Tow_Recovery (拖车救援车)
• Vehicle_Truck_Concrete_Mixer (混凝土搅拌车)
• Vehicle_Truck_Dump_Construction (建筑自卸卡车)
• Vehicle_Truck_Crane_Mobile (移动起重机)
• Vehicle_Bus_City_Transit (城市公交车)
• Vehicle_Bus_School_Yellow (黄色校车)
• Vehicle_Bus_Tour_Charter (旅游包车)
• Vehicle_Taxi_Cab_Urban (城市出租车)
• Vehicle_Police_Car_Patrol (巡逻警车)
• Vehicle_Police_Van_SWAT (特警装甲车)

4.4.3 军用车辆 (Military Vehicles):
• Vehicle_Military_Jeep_Light_Utility (轻型军用吉普车)
• Vehicle_Military_Truck_Transport (军用运输卡车)
• Vehicle_Military_APC_Armored_Personnel (装甲运兵车)
• Vehicle_Military_Tank_Main_Battle (主战坦克)
• Vehicle_Military_Tank_Light_Reconnaissance (轻型侦察坦克)
• Vehicle_Military_Humvee_Armored (装甲悍马车)
• Vehicle_Military_Artillery_Self_Propelled (自行火炮)
• Vehicle_Military_Missile_Launcher_Mobile (移动导弹发射车)
• Vehicle_Military_Command_Vehicle_Mobile (移动指挥车)
• Vehicle_Military_Supply_Truck_Logistics (后勤补给卡车)

4.4.4 特种和应急车辆 (Special and Emergency Vehicles):
• Vehicle_Emergency_Ambulance_Advanced_Life_Support (高级生命支持救护车)
• Vehicle_Emergency_Fire_Truck_Ladder (云梯消防车)
• Vehicle_Emergency_Fire_Truck_Pumper (水泵消防车)
• Vehicle_Emergency_Hazmat_Response (危险品应急响应车)
• Vehicle_Emergency_Bomb_Squad_Disposal (炸弹处理车)
• Vehicle_Emergency_Search_Rescue_Off_Road (越野搜救车)
• Vehicle_Utility_Street_Sweeper (街道清扫车)
• Vehicle_Utility_Snow_Plow_Winter (冬季除雪车)
• Vehicle_Utility_Maintenance_Public_Works (公共工程维护车)
• Vehicle_Utility_Electrical_Line_Service (电力线路服务车)
• Vehicle_Construction_Excavator_Heavy (重型挖掘机)
• Vehicle_Construction_Bulldozer_Earth_Moving (推土机)
• Vehicle_Construction_Loader_Front_End (前端装载机)
• Vehicle_Construction_Grader_Road_Surface (路面平地机)

4.4.5 末日特色车辆 (Post-Apocalyptic Vehicles):
• Vehicle_Apocalypse_Armored_Survivor (装甲幸存者车辆)
• Vehicle_Apocalypse_Mad_Max_Style (疯狂麦克斯风格车辆)
• Vehicle_Apocalypse_Scavenged_Parts (拼凑零件车辆)
• Vehicle_Apocalypse_Mobile_Fortress (移动堡垒车辆)
• Vehicle_Apocalypse_Fuel_Tanker_Armored (装甲燃料运输车)
• Vehicle_Apocalypse_Medical_Mobile_Clinic (移动医疗诊所)
• Vehicle_Apocalypse_Radio_Communication_Mobile (移动无线电通信车)
• Vehicle_Apocalypse_Workshop_Mobile_Repair (移动维修工坊车)
• Vehicle_Apocalypse_Water_Purification_Mobile (移动净水车)
• Vehicle_Apocalypse_Refugee_Transport_Armored (装甲难民运输车)

4.5 公共设施和基础设施 (Public Utilities and Infrastructure)

4.5.1 电力设施 (Electrical Infrastructure):
• Utility_Power_Pole_Wood_Standard (标准木制电线杆)
• Utility_Power_Pole_Concrete_Heavy_Duty (重型混凝土电线杆)
• Utility_Power_Tower_Transmission_High_Voltage (高压输电塔)
• Utility_Transformer_Ground_Mounted (地面安装变压器)
• Utility_Transformer_Pole_Mounted (杆装变压器)
• Utility_Substation_Electrical_Distribution (配电变电站)
• Utility_Generator_Backup_Diesel (柴油备用发电机)
• Utility_Generator_Emergency_Portable (便携式应急发电机)
• Utility_Solar_Panel_Array_Ground (地面太阳能板阵列)
• Utility_Solar_Panel_Tracking_System (太阳能跟踪系统)
• Utility_Wind_Turbine_Small_Residential (小型住宅风力涡轮机)
• Utility_Wind_Turbine_Large_Commercial (大型商用风力涡轮机)
• Utility_Battery_Storage_Grid_Scale (电网规模电池储能)
• Utility_Charging_Station_Electric_Vehicle (电动车充电站)

4.5.2 水务设施 (Water Infrastructure):
• Utility_Water_Hydrant_Fire_Red (红色消防栓)
• Utility_Water_Hydrant_Fire_Yellow (黄色消防栓)
• Utility_Water_Meter_Residential (住宅水表)
• Utility_Water_Meter_Commercial (商用水表)
• Utility_Water_Valve_Shutoff_Street (街道关闭阀)
• Utility_Water_Pump_Station_Booster (增压泵站)
• Utility_Water_Tank_Storage_Elevated (高架储水罐)
• Utility_Water_Tank_Storage_Ground (地面储水罐)
• Utility_Water_Treatment_Plant_Small (小型水处理厂)
• Utility_Water_Well_Pump_Private (私人水井泵)
• Utility_Irrigation_Sprinkler_System (灌溉喷水系统)
• Utility_Irrigation_Drip_System_Efficient (高效滴灌系统)

4.5.3 通信设施 (Communication Infrastructure):
• Utility_Telephone_Pole_Traditional (传统电话杆)
• Utility_Cable_TV_Distribution_Box (有线电视配线箱)
• Utility_Internet_Fiber_Optic_Cabinet (光纤网络机柜)
• Utility_Cell_Tower_Monopole (单杆手机信号塔)
• Utility_Cell_Tower_Lattice_Steel (钢格塔手机信号塔)
• Utility_Satellite_Dish_Large_Commercial (大型商用卫星天线)
• Utility_Radio_Antenna_AM_FM (AM/FM无线电天线)
• Utility_Emergency_Broadcast_System (紧急广播系统)
• Utility_Internet_Kiosk_Public_WiFi (公共WiFi网络亭)
• Utility_Surveillance_Camera_Traffic (交通监控摄像头)

4.5.4 废物管理设施 (Waste Management Infrastructure):
• Utility_Sewer_Manhole_Cover_Standard (标准下水道井盖)
• Utility_Sewer_Vent_Pipe_Odor_Control (下水道通风管)
• Utility_Septic_Tank_Residential (住宅化粪池)
• Utility_Grease_Trap_Commercial_Kitchen (商用厨房隔油池)
• Utility_Storm_Drain_Street_Runoff (街道雨水排水沟)
• Utility_Catch_Basin_Storm_Water (雨水收集池)
• Utility_Recycling_Center_Community (社区回收中心)
• Utility_Compost_Facility_Organic_Waste (有机废物堆肥设施)
• Utility_Landfill_Waste_Disposal (废物填埋场)
• Utility_Incinerator_Waste_To_Energy (垃圾焚烧发电厂)

========================================
5.0 贴花类 (Decal Elements)
========================================

此分类用于在已生成的表面上添加细节和叙事痕迹，增强场景的真实感和故事性。

5.1 损坏和磨损贴花 (Damage and Wear Decals)

5.1.1 结构损坏 (Structural Damage):
• Decal_Crack_Wall_Fine_Hairline (墙壁细发丝裂纹)
• Decal_Crack_Wall_Wide_Branching (墙壁宽分叉裂纹)
• Decal_Crack_Concrete_Settlement (混凝土沉降裂纹)
• Decal_Crack_Asphalt_Thermal (沥青热胀冷缩裂纹)
• Decal_Hole_Bullet_Small_Caliber (小口径子弹孔)
• Decal_Hole_Bullet_Large_Caliber (大口径子弹孔)
• Decal_Hole_Shrapnel_Irregular (不规则弹片孔)
• Decal_Scorch_Explosion_Circular (圆形爆炸焦痕)
• Decal_Scorch_Fire_Spreading (蔓延火焰焦痕)
• Decal_Burn_Mark_Electrical (电气烧痕)
• Decal_Impact_Crater_Small (小型撞击坑)
• Decal_Impact_Crater_Large (大型撞击坑)
• Decal_Scratch_Metal_Deep_Gouge (金属深刮痕)
• Decal_Scratch_Paint_Surface (表面油漆划痕)
• Decal_Dent_Vehicle_Body (车身凹痕)
• Decal_Rust_Spot_Small (小锈斑)
• Decal_Rust_Streak_Vertical (垂直锈迹)
• Decal_Corrosion_Chemical_Acid (化学酸腐蚀)
• Decal_Weathering_Natural_Age (自然老化风化)
• Decal_Erosion_Water_Damage (水侵蚀损坏)

5.1.2 表面污渍 (Surface Stains):
• Decal_Stain_Oil_Dark_Greasy (深色油腻污渍)
• Decal_Stain_Blood_Fresh_Red (新鲜红色血迹)
• Decal_Stain_Blood_Dried_Brown (干涸褐色血迹)
• Decal_Stain_Mud_Splatter (泥浆飞溅)
• Decal_Stain_Water_Ring_Circular (圆形水渍)
• Decal_Stain_Coffee_Brown_Spill (褐色咖啡溢出)
• Decal_Stain_Paint_Drip_Vertical (垂直油漆滴落)
• Decal_Stain_Grease_Automotive (汽车润滑脂)
• Decal_Stain_Chemical_Discoloration (化学变色)
• Decal_Stain_Soot_Smoke_Black (黑色烟灰)
• Decal_Stain_Mold_Green_Damp (潮湿绿色霉斑)
• Decal_Stain_Algae_Slippery_Surface (滑腻藻类表面)
• Decal_Stain_Urine_Yellow_Dried (干涸黄色尿渍)
• Decal_Stain_Vomit_Chunky_Disgusting (恶心呕吐物)
• Decal_Stain_Tar_Black_Sticky (黑色粘性焦油)
• Decal_Stain_Ash_Gray_Powdery (灰色粉状灰烬)

5.2 涂鸦和标记 (Graffiti and Markings)

5.2.1 文字涂鸦 (Text Graffiti):
• Graffiti_Text_Warning_Danger_Ahead (前方危险警告文字)
• Graffiti_Text_Safe_Zone_Direction (安全区方向文字)
• Graffiti_Text_Survivor_Count_Tally (幸存者计数标记)
• Graffiti_Text_Date_Last_Seen (最后见面日期)
• Graffiti_Text_Missing_Person_Name (失踪人员姓名)
• Graffiti_Text_Gang_Territory_Claim (帮派领土声明)
• Graffiti_Text_Political_Resistance (政治抵抗标语)
• Graffiti_Text_Religious_Prayer (宗教祈祷文)
• Graffiti_Text_Profanity_Crude (粗俗脏话)
• Graffiti_Text_Love_Declaration (爱情宣言)
• Graffiti_Text_Coordinates_Location (位置坐标)
• Graffiti_Text_Supply_Cache_Hidden (隐藏物资缓存)

5.2.2 符号涂鸦 (Symbol Graffiti):
• Graffiti_Symbol_Arrow_Direction (方向箭头)
• Graffiti_Symbol_X_Mark_Negative (X标记否定)
• Graffiti_Symbol_Check_Mark_Positive (勾号标记肯定)
• Graffiti_Symbol_Skull_Danger (骷髅危险符号)
• Graffiti_Symbol_Cross_Religious (宗教十字符号)
• Graffiti_Symbol_Heart_Love (爱心符号)
• Graffiti_Symbol_Star_Rating (星级评分)
• Graffiti_Symbol_Circle_Target (圆圈目标)
• Graffiti_Symbol_Triangle_Warning (三角警告)
• Graffiti_Symbol_Spiral_Confusion (螺旋混乱)
• Graffiti_Symbol_Biohazard_Contamination (生物危险污染)
• Graffiti_Symbol_Radiation_Nuclear (核辐射符号)

5.2.3 艺术涂鸦 (Artistic Graffiti):
• Graffiti_Art_Mural_Colorful (彩色壁画)
• Graffiti_Art_Portrait_Face (人物肖像)
• Graffiti_Art_Abstract_Geometric (抽象几何)
• Graffiti_Art_Cartoon_Character (卡通角色)
• Graffiti_Art_Landscape_Scene (风景场景)
• Graffiti_Art_Animal_Wildlife (野生动物)
• Graffiti_Art_Flower_Nature (自然花卉)
• Graffiti_Art_Mandala_Spiritual (精神曼陀罗)
• Graffiti_Art_Tribal_Pattern (部落图案)
• Graffiti_Art_Stencil_Repeated (重复模板)

5.3 环境痕迹 (Environmental Traces)

5.3.1 自然痕迹 (Natural Traces):
• Trace_Footprint_Human_Muddy (人类泥泞脚印)
• Trace_Footprint_Animal_Paw (动物爪印)
• Trace_Tire_Track_Vehicle (车辆轮胎痕迹)
• Trace_Drag_Mark_Heavy_Object (重物拖拽痕迹)
• Trace_Skid_Mark_Brake_Rubber (刹车橡胶痕迹)
• Trace_Water_Puddle_Reflection (反光水坑)
• Trace_Leaf_Litter_Autumn (秋季落叶堆积)
• Trace_Snow_Drift_Wind_Blown (风吹雪堆)
• Trace_Sand_Ripple_Desert (沙漠沙纹)
• Trace_Mud_Crack_Dried (干涸泥裂)
• Trace_Ice_Crystal_Frost (霜冰晶体)
• Trace_Pollen_Dust_Yellow (黄色花粉尘)

5.3.2 人为痕迹 (Human-Made Traces):
• Trace_Chalk_Outline_Body (粉笔人体轮廓)
• Trace_Tape_Crime_Scene_Yellow (黄色犯罪现场胶带)
• Trace_Spray_Paint_Mark_Utility (公用事业喷漆标记)
• Trace_Caution_Tape_Orange (橙色警戒胶带)
• Trace_Barricade_Tape_Red_White (红白路障胶带)
• Trace_Evidence_Marker_Number (证据标记号码)
• Trace_Survey_Flag_Bright_Color (鲜艳测量旗)
• Trace_Construction_Mark_Temporary (临时施工标记)
• Trace_Utility_Mark_Underground (地下公用设施标记)
• Trace_Property_Line_Boundary (财产界线边界)

5.4 特殊效果贴花 (Special Effect Decals)

5.4.1 光影效果 (Light and Shadow Effects):
• Effect_Shadow_Tree_Dappled (斑驳树影)
• Effect_Shadow_Building_Sharp (建筑锐利阴影)
• Effect_Light_Beam_Sunlight (阳光光束)
• Effect_Light_Reflection_Water (水面光反射)
• Effect_Light_Caustic_Swimming_Pool (游泳池焦散光)
• Effect_Glow_Neon_Sign (霓虹灯发光)
• Effect_Glow_Fire_Warm (火焰温暖光晕)
• Effect_Glow_Radioactive_Green (放射性绿光)
• Effect_Sparkle_Glass_Broken (破碎玻璃闪光)
• Effect_Shimmer_Heat_Mirage (热浪蜃景闪烁)

5.4.2 天气效果 (Weather Effects):
• Effect_Rain_Drop_Splatter (雨滴飞溅)
• Effect_Rain_Streak_Window (窗户雨痕)
• Effect_Snow_Accumulation_Corner (角落积雪)
• Effect_Ice_Formation_Icicle (冰柱形成)
• Effect_Frost_Pattern_Window (窗户霜花图案)
• Effect_Condensation_Fog_Glass (玻璃雾气凝结)
• Effect_Wind_Dust_Swirl (风尘旋涡)
• Effect_Hail_Damage_Dent (冰雹损坏凹痕)
• Effect_Lightning_Scorch_Tree (雷击树木焦痕)
• Effect_Flood_Water_Line (洪水水位线)

========================================
6.0 状态修饰符 (State Modifiers)
========================================

这些标签可以与上述任何元素组合使用，描述其当前状态和条件。

6.1 通用状态 (General States):
• State_New_Pristine (全新完美)
• State_Good_Well_Maintained (良好维护)
• State_Fair_Some_Wear (一般磨损)
• State_Poor_Significant_Damage (较差显著损坏)
• State_Ruined_Severely_Damaged (严重损坏废墟)
• State_Abandoned_Neglected (遗弃忽视)
• State_Under_Construction (建设中)
• State_Under_Repair (维修中)
• State_Demolished_Partially (部分拆除)
• State_Collapsed_Structural_Failure (结构失效坍塌)

6.2 环境状态 (Environmental States):
• State_Wet_Rain_Soaked (雨水浸湿)
• State_Dry_Sun_Baked (阳光烘烤干燥)
• State_Frozen_Ice_Covered (冰雪覆盖)
• State_Muddy_Soil_Saturated (土壤饱和泥泞)
• State_Dusty_Powder_Coated (粉尘覆盖)
• State_Moldy_Damp_Growth (潮湿霉菌生长)
• State_Rusty_Metal_Oxidized (金属氧化生锈)
• State_Faded_Sun_Bleached (阳光漂白褪色)
• State_Cracked_Thermal_Stress (热应力开裂)
• State_Warped_Moisture_Damage (湿气损坏变形)

6.3 末日特色状态 (Post-Apocalyptic States):
• State_Radioactive_Contaminated (放射性污染)
• State_Toxic_Chemical_Exposure (有毒化学暴露)
• State_Infected_Biological_Hazard (生物危险感染)
• State_Quarantined_Sealed_Off (隔离封锁)
• State_Fortified_Defensive_Barriers (防御加固)
• State_Scavenged_Parts_Removed (拆除零件清理)
• State_Overgrown_Nature_Reclaimed (自然重新占领)
• State_Burned_Fire_Damaged (火灾损坏烧毁)
• State_Looted_Ransacked_Empty (抢劫洗劫一空)
• State_Makeshift_Jury_Rigged (临时拼凑修理)

========================================
总结 (Summary)
========================================

这份详细的标签系统为Unity中的程序化Low Poly末日场景生成提供了全面的分类框架。通过组合使用这些标签，开发者可以：

1. **创建丰富多样的场景** - 从自然环境到人造结构，从完好设施到末日废墟
2. **实现智能化生成** - 基于标签逻辑自动选择合适的资产组合
3. **保持视觉一致性** - 通过状态修饰符确保场景元素风格统一
4. **增强叙事表现** - 利用贴花和环境痕迹讲述末日世界的故事
5. **优化性能表现** - Low Poly风格确保在移动平台上的流畅运行

建议在实际应用中根据项目需求和性能预算，选择性地实现这些标签类别，并建立相应的资产库和生成规则。
