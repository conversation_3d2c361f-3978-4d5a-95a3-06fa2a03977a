using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;

/// <summary>
/// 顶点色设置组件
///
/// 工作流程：
/// 1. 自动创建临时预览材质球，通过Shader实时显示颜色效果
/// 2. 支持27个分段颜色（RGB三通道，每通道9个区间）
/// 3. R通道由SceneRChannelController统一管理，G/B通道独立设置
///
/// 特性：
/// - 临时材质球：仅存在于内存中，不保存到项目资源
/// - 独立实例：每个组件使用独立的材质球，支持复制物件
/// - 自动恢复：检测材质球丢失并自动重新创建
/// - 数据持久化：用户设置的颜色自动保存，重新打开场景不会丢失
/// </summary>
[ExecuteAlways]
public class SetVertexColor : MonoBehaviour
{
    // 用于存储27个不同颜色的数组，对应顶点RGB三个通道的9个区间
    // R通道：索引0-8, G通道：索引9-17, B通道：索引18-26
    // 区间分别为：0.1、0.1889、0.2778、0.3667、0.4556、0.5444、0.6333、0.7222、0.9
    public Color[] sectionColors = new Color[27];

    // 原始的单一颜色设置（已弃用，保留向后兼容性）
    [System.Obsolete("单一颜色模式已弃用，现在默认使用分段颜色模式")]
    public Color vertexColor = Color.white;

    // 标记是否已经应用了颜色
    private bool colorApplied = false;

    // 缓存组件引用
    private MeshFilter meshFilter;
    private Renderer meshRenderer;
    
    // 静态共享的MaterialPropertyBlock
    private static MaterialPropertyBlock sharedPropertyBlock;

    // 颜色数组缓存
    private Color[] colorCache;

    // 缓存R通道控制器，避免每帧查找
    private SceneRChannelController cachedRChannelController;
    private float lastControllerCheckTime = 0f;

    // 材质球管理
    [Header("材质球设置")]
    [SerializeField]
    public Material previewMaterial;   // 预览材质球

    // 预览模式下的材质属性名称（27个颜色属性）
    private static readonly string[] COLOR_PROPERTIES = {
        // R通道 (0-8)
        "_ColorR0", "_ColorR1", "_ColorR2", "_ColorR3", "_ColorR4",
        "_ColorR5", "_ColorR6", "_ColorR7", "_ColorR8",
        // G通道 (9-17)
        "_ColorG0", "_ColorG1", "_ColorG2", "_ColorG3", "_ColorG4",
        "_ColorG5", "_ColorG6", "_ColorG7", "_ColorG8",
        // B通道 (18-26)
        "_ColorB0", "_ColorB1", "_ColorB2", "_ColorB3", "_ColorB4",
        "_ColorB5", "_ColorB6", "_ColorB7", "_ColorB8"
    };

    // 预览shader路径
    private static readonly string PREVIEW_SHADER_NAME = "Custom/CityBuilding02_VertexColor";

    // 材质球状态检查
    private float lastMaterialCheckTime = 0f;
    private const float MATERIAL_CHECK_INTERVAL = 1.0f; // 每秒检查一次材质球状态
    
    // 颜色初始化标记
    [SerializeField]
    private bool colorsInitialized = false;

#if UNITY_EDITOR
    void OnValidate()
    {
        // 确保在编辑器中修改属性时立即更新颜色，并应用颜色变化
        colorApplied = false;

        // 确保sectionColors数组大小为27
        if (sectionColors == null || sectionColors.Length != 27)
        {
            Color[] newColors = new Color[27];
            if (sectionColors != null)
            {
                for (int i = 0; i < Mathf.Min(sectionColors.Length, 27); i++)
                {
                    newColors[i] = sectionColors[i];
                }
            }
            sectionColors = newColors;
        }

        // 更新预览材质球的颜色
        UpdatePreviewMaterialColors();
    }
#endif

    // 在组件被重置时初始化颜色数组
    private void Reset()
    {
        // 重置初始化标记，允许重新初始化
        colorsInitialized = false;
        
        // 初始化27个颜色：R通道(0-8)由SceneRChannelController管理，只初始化G通道(9-17)、B通道(18-26)
        for (int i = 0; i < 27; i++)
        {
            int channelIndex = i % 9; // 在通道内的索引 (0-8)
            int channel = i / 9;      // 通道编号 (0=R, 1=G, 2=B)

            // 计算亮度值，从1（白色）递减到0（黑色）
            float brightness = 1.0f - (float)channelIndex / 8.0f;

            // 根据通道设置颜色
            if (channel == 0) // R通道
            {
                // R通道颜色由SceneRChannelController管理，这里设置为透明占位
                sectionColors[i] = Color.clear;
            }
            else if (channel == 1) // G通道
            {
                sectionColors[i] = new Color(0, brightness, 0, 1.0f);
            }
            else // B通道
            {
                sectionColors[i] = new Color(0, 0, brightness, 1.0f);
            }
        }
        
        // 标记为已初始化
        colorsInitialized = true;

        // 重置后不再自动应用颜色，只在用户点击应用按钮时才执行
    }

    private void Awake()
    {
        // 缓存组件引用
        meshFilter = GetComponent<MeshFilter>();
        meshRenderer = GetComponent<Renderer>();

        // 确保静态MaterialPropertyBlock已初始化    
        if (sharedPropertyBlock == null)
        {
            sharedPropertyBlock = new MaterialPropertyBlock();
        }

#if UNITY_EDITOR
        // 检查预览材质球是否属于当前实例，如果不属于则清理引用
        if (previewMaterial != null)
        {
            string expectedMaterialName = gameObject.name + "_PreviewMaterial_" + GetInstanceID();
            if (previewMaterial.name != expectedMaterialName)
            {
                previewMaterial = null;
            }
        }
#endif

        // 确保sectionColors数组中的所有元素都有有效的颜色值
        InitializeColorsIfNeeded();

        // 初始化颜色缓存
        if (meshFilter != null && meshFilter.sharedMesh != null)
        {
            Mesh sharedMesh = meshFilter.sharedMesh;
            int vertexCount = sharedMesh.vertexCount;

            // 初始化颜色缓存
            colorCache = new Color[vertexCount];
        }
    }

    private void OnEnable()
    {
        // 确保颜色数组已初始化 
        InitializeColorsIfNeeded();
        
        // 通知R通道控制器有新组件启用，确保应用全局R通道颜色
        NotifyRChannelController();
        
        // 检查是否需要创建新的材质球
        bool needNewMaterial = false;
        
        if (previewMaterial == null)
        {
            needNewMaterial = true;
        }
        else
        {
            // 检查当前材质球是否属于这个实例（通过名称中的实例ID判断）
            string expectedMaterialName = gameObject.name + "_PreviewMaterial_" + GetInstanceID();
            if (previewMaterial.name != expectedMaterialName)
            {
                needNewMaterial = true;
            }
        }
        
        if (needNewMaterial)
        {
            CreatePreviewMaterial();
        }
        else
        {
            // 即使材质球存在，也强制更新颜色以确保颜色正确应用
            UpdatePreviewMaterialColors();
        }
        
        // 延迟应用R通道颜色，避免闪烁
#if UNITY_EDITOR
        UnityEditor.EditorApplication.delayCall += () => {
            if (this != null && previewMaterial != null)
            {
                // 查找R通道控制器并应用颜色
                var rChannelController = FindObjectOfType<SceneRChannelController>();
                if (rChannelController != null)
                {
                    rChannelController.ApplyRChannelColorsToMaterial(this, previewMaterial);
                }
            }
        };
#endif
    }
    
    private void Start()
    {
#if UNITY_EDITOR
        // 在Start阶段确保颜色正确应用（此时场景完全加载）
        if (previewMaterial != null)
        {
            UpdatePreviewMaterialColors();
            
            // 确保R通道颜色也正确应用
            if (cachedRChannelController != null)
            {
                cachedRChannelController.ApplyRChannelColorsToMaterial(this, previewMaterial);
            }
        }
#endif
    }
    
    private void OnDestroy()
    {
#if UNITY_EDITOR
        // 清理临时材质球
        if (previewMaterial != null)
        {
            DestroyImmediate(previewMaterial);
            previewMaterial = null;
        }
#endif
    }
    
#if UNITY_EDITOR
    private void Update()
    {
        // 每秒检查一次R通道控制器
        if (Time.realtimeSinceStartup - lastControllerCheckTime > 1.0f)
        {
            SceneRChannelController previousController = cachedRChannelController;
            
            if (cachedRChannelController == null || !cachedRChannelController)
            {
                cachedRChannelController = FindObjectOfType<SceneRChannelController>();
                
                // 如果控制器刚刚变为可用，立即应用R通道颜色
                if (cachedRChannelController != null && previousController == null && previewMaterial != null)
                {
                    cachedRChannelController.ApplyRChannelColorsToMaterial(this, previewMaterial);
                }
            }
            lastControllerCheckTime = Time.realtimeSinceStartup;
        }

        // 应用R通道颜色到预览材质球
        if (cachedRChannelController != null && previewMaterial != null)
        {
            cachedRChannelController.ApplyRChannelColorsToMaterial(this, previewMaterial);
        }
        
        // 定期检查材质球状态，防止材质球丢失
        if (Time.realtimeSinceStartup - lastMaterialCheckTime > MATERIAL_CHECK_INTERVAL)
        {
            CheckAndRecreatePreviewMaterial();
            lastMaterialCheckTime = Time.realtimeSinceStartup;
        }
    }
#endif
    
    // 通知R通道控制器有组件变化
    private void NotifyRChannelController()
    {
        // 清除缓存，下次Update时会重新查找
        cachedRChannelController = null;
        lastControllerCheckTime = 0f;
    }

    // 确保颜色数组已初始化
    private void InitializeColorsIfNeeded()
    {
        // 强制确保sectionColors数组大小为27
        if (sectionColors == null)
        {
            sectionColors = new Color[27];
        }
        else if (sectionColors.Length != 27)
        {
            // 创建一个新的27元素数组
            Color[] newColors = new Color[27];

            // 复制现有颜色（如果有的话）
            for (int i = 0; i < Mathf.Min(sectionColors.Length, 27); i++)
            {
                newColors[i] = sectionColors[i];
            }

            // 替换原数组
            sectionColors = newColors;
        }

        // 只有在从未初始化过的情况下才进行初始化
        if (!colorsInitialized)
        {
            // 设置27个颜色：R通道(0-8)由SceneRChannelController管理，只初始化G通道(9-17)、B通道(18-26)
            for (int i = 0; i < 27; i++)
            {
                int channelIndex = i % 9; // 在通道内的索引 (0-8)
                int channel = i / 9;      // 通道编号 (0=R, 1=G, 2=B)

                // 计算亮度值，从1（白色）递减到0（黑色）
                float brightness = 1.0f - (float)channelIndex / 8.0f;

                // 根据通道设置颜色
                if (channel == 0) // R通道
                {
                    // R通道颜色由SceneRChannelController管理，这里设置为透明占位
                    sectionColors[i] = Color.clear;
                }
                else if (channel == 1) // G通道
                {
                    sectionColors[i] = new Color(0, brightness, 0, 1.0f);
                }
                else // B通道
                {
                    sectionColors[i] = new Color(0, 0, brightness, 1.0f);
                }
            }
            
            // 标记为已初始化
            colorsInitialized = true;
            
            // 标记组件为已修改，确保序列化保存
#if UNITY_EDITOR
            UnityEditor.EditorUtility.SetDirty(this);
#endif
        }
    }

    // 获取指定索引的颜色
    public Color GetSectionColor(int index)
    {
        if (index >= 0 && index < 27)
        {
            // R通道颜色（索引0-8）从SceneRChannelController获取
            if (index >= 0 && index <= 8)
            {
                // 使用缓存的控制器
                if (cachedRChannelController != null)
                {
                    return cachedRChannelController.GetRChannelColor(index);
                }
                // 如果没有控制器，返回默认R通道颜色
                float brightness = 1.0f - (float)index / 8.0f;
                return new Color(brightness, 0, 0, 1.0f);
            }
            
            return sectionColors[index];
        }
        return Color.white;
    }

    // 设置指定索引的颜色
    public void SetSectionColor(int index, Color color)
    {
        if (index >= 0 && index < 27)
        {
            // R通道颜色（索引0-8）应该通过SceneRChannelController设置
            if (index >= 0 && index <= 8)
            {
                // 使用缓存的控制器
                if (cachedRChannelController != null)
                {
                    cachedRChannelController.SetRChannelColor(index, color);
                    return;
                }
            }
            
            sectionColors[index] = color;
            // 只更新预览材质球，不自动应用到mesh
#if UNITY_EDITOR
            UpdatePreviewMaterialColors();
#endif
        }
    }

    // 公开方法：更新预览材质球颜色（供外部工具调用）
    public void UpdatePreviewMaterialColorsPublic()
    {
#if UNITY_EDITOR
        UpdatePreviewMaterialColors();
#endif
    }

    // 公开方法：强制立即更新R通道颜色（供外部工具调用）
    public void ForceUpdateRChannelColors()
    {
#if UNITY_EDITOR
        // 使用缓存的控制器
        if (cachedRChannelController != null && previewMaterial != null)
        {
            cachedRChannelController.ApplyRChannelColorsToMaterial(this, previewMaterial);
        }
#endif
    }

    // 通知所有SetVertexColor组件更新R通道颜色（静态方法）
    public static void NotifyAllComponentsUpdateRChannel()
    {
        // 移除大量FindObjectsOfType调用，改为依赖Update方法的缓存机制
        // 这样可以避免卡顿，同时保持功能正常
    }

#if UNITY_EDITOR
    // 更新预览材质球的颜色（只处理GB通道，R通道由SceneRChannelController统一管理）
    private void UpdatePreviewMaterialColors()
    {
        if (previewMaterial != null)
        {
            // 只设置GB通道颜色（索引9-26），R通道由SceneRChannelController统一管理
            for (int i = 9; i < COLOR_PROPERTIES.Length && i < 27; i++)
            {
                Color colorToSet;
                
                if (i < sectionColors.Length)
                {
                    colorToSet = sectionColors[i];
                }
                else
                {
                    colorToSet = Color.white;
                }
                
                previewMaterial.SetColor(COLOR_PROPERTIES[i], colorToSet);
            }
        }
    }

    // 创建预览材质球
    public void CreatePreviewMaterial()
    {
        if (meshRenderer == null)
        {
            Debug.LogWarning("无法创建预览材质球：MeshRenderer组件不存在");
            return;
        }

        // 查找预览shader
        Shader previewShader = Shader.Find(PREVIEW_SHADER_NAME);
        if (previewShader == null)
        {
            Debug.LogError("无法找到预览shader: " + PREVIEW_SHADER_NAME);
            return;
        }

        // 创建临时材质球（不保存到磁盘）
        Material newMaterial = new Material(previewShader);
        newMaterial.name = gameObject.name + "_PreviewMaterial_" + GetInstanceID();

        // 获取当前材质球的贴图和属性进行复制
        Material currentMaterial = meshRenderer.sharedMaterial;
        if (currentMaterial != null)
        {
            // 复制贴图
            if (currentMaterial.HasProperty("_Diffuse") && newMaterial.HasProperty("_Diffuse"))
            {
                newMaterial.SetTexture("_Diffuse", currentMaterial.GetTexture("_Diffuse"));
            }

            // 复制其他通用属性
            if (currentMaterial.HasProperty("_Color") && newMaterial.HasProperty("_Color"))
            {
                newMaterial.SetColor("_Color", currentMaterial.GetColor("_Color"));
            }

            if (currentMaterial.HasProperty("_Cutout") && newMaterial.HasProperty("_Cutout"))
            {
                newMaterial.SetFloat("_Cutout", currentMaterial.GetFloat("_Cutout"));
            }
        }

        // 更新引用
        previewMaterial = newMaterial;

        // 应用材质球到渲染器
        meshRenderer.sharedMaterial = previewMaterial;

        // 设置分段颜色
        UpdatePreviewMaterialColors();

        // 立即应用R通道颜色（如果控制器可用）
        if (cachedRChannelController != null)
        {
            cachedRChannelController.ApplyRChannelColorsToMaterial(this, previewMaterial);
        }

        // 标记组件为已修改
        UnityEditor.EditorUtility.SetDirty(this);
    }

    // 检查并重新创建预览材质球（防止材质球丢失）
    private void CheckAndRecreatePreviewMaterial()
    {
        // 检查预览材质球是否丢失
        bool needRecreate = false;
        
        // 检查材质球对象是否为空
        if (previewMaterial == null)
        {
            needRecreate = true;
        }
        else
        {
            // 检查当前材质球是否属于这个实例（通过名称中的实例ID判断）
            string expectedMaterialName = gameObject.name + "_PreviewMaterial_" + GetInstanceID();
            if (previewMaterial.name != expectedMaterialName)
            {
                needRecreate = true;
            }
        }
        
        // 检查渲染器是否还使用预览材质球
        if (meshRenderer != null && previewMaterial != null && meshRenderer.sharedMaterial != previewMaterial)
        {
            needRecreate = true;
        }
        
        if (needRecreate)
        {
            CreatePreviewMaterial();
        }
    }

    // 应用颜色的主方法
    private void ApplyColors()
    {
        // 如果已经应用过颜色且没有变化，则跳过
        if (colorApplied)
            return;

        // 只更新预览材质球参数
        if (previewMaterial != null)
        {
            UpdatePreviewMaterialColors();
            colorApplied = true;
            return;
        }

        // 标记颜色已应用
        colorApplied = true;
    }
#endif

    // 缓存的R通道分析结果
    private HashSet<float> cachedEffectiveRValues = null;
    private bool rAnalysisValid = false;

    // 获取当前组件的有效R通道区间（使用缓存的分析结果）
    public HashSet<float> GetEffectiveRValues()
    {
        // 如果分析结果无效或者缓存为空，重新分析
        if (!rAnalysisValid || cachedEffectiveRValues == null)
        {
            AnalyzeRChannelData();
        }
        
        return cachedEffectiveRValues ?? new HashSet<float>();
    }

    // 分析R通道数据（与SetVertexColorEditor相同的逻辑）
    private void AnalyzeRChannelData()
    {
        if (cachedEffectiveRValues == null)
        {
            cachedEffectiveRValues = new HashSet<float>();
        }
        else
        {
            cachedEffectiveRValues.Clear();
        }
        
        var meshFilter = GetComponent<MeshFilter>();
        if (meshFilter == null || meshFilter.sharedMesh == null)
        {
            rAnalysisValid = true;
            return;
        }

        var mesh = meshFilter.sharedMesh;
        
        // 使用与SetVertexColorEditor完全相同的方式分析R通道
        Color[] colors;
        try
        {
            colors = mesh.colors;
        }
        catch
        {
            // 静默处理读取权限问题
            rAnalysisValid = true;
            return;
        }

        if (colors == null || colors.Length == 0)
        {
            rAnalysisValid = true;
            return;
        }

        // 定义9个区间的中心值（与SetVertexColorEditor完全相同，从小到大排列）
        float[] sectionCenterValues = {
            0.1f,      // 区间0
            0.1889f,   // 区间1
            0.2778f,   // 区间2
            0.3667f,   // 区间3
            0.4556f,   // 区间4
            0.5444f,   // 区间5
            0.6333f,   // 区间6
            0.7222f,   // 区间7
            0.9f       // 区间8
        };

        const float tolerance = 0.01f; // 允许±0.01的误差范围
        const float zeroTolerance = 0.01f; // 判断是否为0的容差

        // 分析每个顶点色，检查R通道（与SetVertexColorEditor相同的逻辑）
        foreach (var color in colors)
        {
            // 检查R通道（只有当G=0且B=0时才生效）
            if (Mathf.Abs(color.g) <= zeroTolerance && Mathf.Abs(color.b) <= zeroTolerance)
            {
                foreach (var centerValue in sectionCenterValues)
                {
                    if (Mathf.Abs(color.r - centerValue) <= tolerance)
                    {
                        cachedEffectiveRValues.Add(centerValue);
                        break;
                    }
                }
            }
        }

        rAnalysisValid = true;
    }

    // 标记R通道分析需要重新进行（当顶点色数据变化时调用）
    public void InvalidateRChannelAnalysis()
    {
        rAnalysisValid = false;
    }
}
