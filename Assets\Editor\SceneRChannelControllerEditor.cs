using UnityEngine;
using UnityEditor;

[CustomEditor(typeof(SceneRChannelController))]
public class SceneRChannelControllerEditor : Editor
{
    private SerializedProperty rChannelColorsProperty;

    // R通道区间值定义（从小到大排列）
    private static readonly float[] R_SECTION_VALUES = {
        0.1f, 0.1889f, 0.2778f, 0.3667f, 0.4556f,
        0.5444f, 0.6333f, 0.7222f, 0.9f
    };

    private void OnEnable()
    {
        // 获取序列化属性
        rChannelColorsProperty = serializedObject.FindProperty("rChannelColors");
    }

    public override void OnInspectorGUI()
    {
        serializedObject.Update();

        SceneRChannelController controller = (SceneRChannelController)target;

        EditorGUILayout.Space();

        // 标题
        EditorGUILayout.LabelField("场景R通道统一颜色控制器", EditorStyles.boldLabel);
        
        // 自动检查场景变化并刷新分析
        AutoRefreshAnalysis(controller);

        // 显示场景组件信息
        int componentCount = controller.GetSceneComponentCount();
        EditorGUILayout.LabelField($"场景组件: {componentCount} 个 SetVertexColor");

        // 显示有效R通道区间信息
        int effectiveRCount = controller.GetEffectiveRValuesCount();
        EditorGUILayout.LabelField($"有效R通道区间: {effectiveRCount} 个");
        
        if (effectiveRCount > 0)
        {
            EditorGUILayout.HelpBox(controller.GetEffectiveRValuesDescription(), MessageType.Info);
        }
        else
        {
            EditorGUILayout.HelpBox("未检测到有效的R通道区间。系统已自动尝试启用模型读取权限。", MessageType.Warning);
        }

        if (componentCount == 0)
        {
            EditorGUILayout.HelpBox("场景中没有找到SetVertexColor组件。请先为模型添加SetVertexColor组件。", MessageType.Warning);
        }

        EditorGUILayout.Space();

        // 确保数组大小为9
        if (rChannelColorsProperty.arraySize != 9)
        {
            rChannelColorsProperty.arraySize = 9;
        }

        // 绘制R通道颜色设置
        EditorGUILayout.LabelField("R通道统一颜色设置", EditorStyles.boldLabel);

        for (int i = 0; i < 9; i++)
        {
            // 创建标签内容
            string baseLabel = $"{R_SECTION_VALUES[i]:F4}附近";
            GUIContent labelContent = new GUIContent($"R{i}: {baseLabel}");

            // 检查这个区间是否有效
            bool isEffective = controller.IsRValueEffective(R_SECTION_VALUES[i]);

            // 根据有效性设置文字颜色
            if (!isEffective)
            {
                // 保存原始颜色
                Color originalColor = GUI.contentColor;
                GUI.contentColor = Color.gray; // 使用灰色表示无效区间

                EditorGUILayout.PropertyField(
                    rChannelColorsProperty.GetArrayElementAtIndex(i),
                    labelContent
                );

                // 恢复原始颜色
                GUI.contentColor = originalColor;
            }
            else
            {
                // 有效区间使用正常颜色
                EditorGUILayout.PropertyField(
                    rChannelColorsProperty.GetArrayElementAtIndex(i),
                    labelContent
                );
            }
        }

        // 添加说明
        EditorGUILayout.HelpBox("统一控制说明：\n• 这里设置的颜色将通过Shader.SetGlobalColor()应用到全局\n• 所有使用相同Shader的物体都会受到影响\n• R通道区间值：0.1、0.1889、0.2778、0.3667、0.4556、0.5444、0.6333、0.7222、0.9\n• G和B通道依然在各个组件中单独控制\n• 灰色显示的区间表示在当前场景中无效", MessageType.Info);

        // 绘制分隔线
        EditorGUILayout.Space();
        EditorGUILayout.LabelField("", GUI.skin.horizontalSlider);
        EditorGUILayout.Space();

        // 应用序列化属性变化并检测是否需要更新颜色
        bool propertiesChanged = GUI.changed;
        serializedObject.ApplyModifiedProperties();
        
        // 如果属性发生变化，立即应用颜色变化
        if (propertiesChanged)
        {
            controller.ApplyRChannelColors(false);
        }

        EditorGUILayout.Space();

        // 使用说明
        EditorGUILayout.LabelField("使用说明", EditorStyles.boldLabel);
        EditorGUILayout.HelpBox("使用说明：\n• 系统会自动扫描和监控场景中所有SetVertexColor组件的R通道使用情况\n• 当场景中的组件发生变化时，会自动重新分析\n• 调整上方的R通道颜色设置，修改会自动应用到全局\n• 灰色显示的区间在当前场景中无效\n• 同时更新全局Shader属性和现有材质球", MessageType.None);
    }

    // 自动刷新分析
    private void AutoRefreshAnalysis(SceneRChannelController controller)
    {
        // 检查场景是否需要重新分析
        if (controller.NeedsRefresh())
        {
            controller.RefreshSceneComponents();
            Repaint(); // 强制重绘界面
        }
    }
} 